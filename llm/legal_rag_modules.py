"""
DSPy-based modules for Legal RAG (Retrieval-Augmented Generation) application.

This module provides a streamlined DSPy implementation specifically designed for
handling retrieval-augmented generation in legal and procedural knowledge domains.
It implements an optimized multi-hop RAG functionality using the DSPy framework.

The module includes:
1. IntentExtractionModule for extracting intent and entities from legal queries
2. MultiHopRAGModule that implements an efficient two-stage retrieval process
3. InstructionSynthesizer for composing tailored responses based on procedural context
4. LegalRAGResponseGenerator signature for generating legal responses
5. A streamlined KnowledgeBaseChatbot that manages conversation flow

These components work together to provide an efficient multi-hop RAG implementation that
understands user intent, retrieves relevant legal information, and generates accurate,
tailored responses with procedural context for user queries.

Key features:
- Proper DSPy module composition for optimization support
- Multi-hop retrieval for complex procedural queries
- Smart result deduplication
- Intent-based query processing
- Composable architecture following DSPy patterns
"""

from typing import Any, Dict, List, Optional
import time
import asyncio
import hashlib
from concurrent.futures import ThreadPoolExecutor

import dspy


class IntentExtractionModule(dspy.Module):
    """
    DSPy module for extracting intent and entities from legal queries.
    Follows proper DSPy patterns for composability and optimization.
    """

    def __init__(self):
        super().__init__()
        # Use DSPy's ChainOfThought for intent extraction
        self.intent_extractor = dspy.ChainOfThought(IntentEntityExtractor)

    def forward(self, query):
        """
        Extract intent and entities using DSPy's optimization framework.

        Args:
            query: The user's question

        Returns:
            DSPy Prediction with intent analysis
        """
        return self.intent_extractor(query=query)


class IntentEntityExtractor(dspy.Signature):
    """
    You are an expert at extracting the intent and entities from legal queries.
    Your goal is to accurately identify the primary task or process being asked about (intent)
    and the key contextual entities mentioned in the query.

    For a legal query, the intent might be:
    - Request for procedure (how to do something)
    - Request for information (what is something)
    - Request for clarification (explain something)
    - Request for verification (is something allowed/valid)
    - Request for requirements (what is needed for something)

    Entities are contextual elements such as:
    - Legal concepts (contracts, torts, liability, etc.)
    - Document types (agreement, policy, statute, regulation)
    - Roles or parties (client, vendor, regulator, employee)
    - Conditions or qualifiers (timeframe, jurisdiction, exceptions)
    """
    query: str = dspy.InputField(desc="The user's question")

    intent: str = dspy.OutputField(desc="The primary intent or task type in the query")
    intent_category: str = dspy.OutputField(desc="Category of intent: procedure, information, clarification, verification, or requirements")
    entities: list[str] = dspy.OutputField(desc="List of key entities extracted from the query")
    entity_types: dict[str, str] = dspy.OutputField(desc="Mapping of each entity to its type (concept, document, role, condition)")
    inferred_concepts: list[str] = dspy.OutputField(desc="Additional legal concepts that are implied but not explicitly mentioned")


# Removed unused signature classes:
# - SimpleKBRetriever (not used)
# - QuestionRearticulator (only used in EnhancedRAGModule)
# - ContextualAnalyzer (only used in EnhancedRAGModule)
# - ProcedureAdapter (only used in EnhancedRAGModule)

class StepExtractor(dspy.Signature):
    """
    You are an expert at extracting relevant steps or procedures from similar situations.
    Your goal is to identify procedures, steps, or guidelines that could be applied to the current query.
    """
    query: str = dspy.InputField(desc="The user's question")
    retrieved_context: str = dspy.InputField(desc="The retrieved documents from the knowledge base")

    similar_situations: list[str] = dspy.OutputField(
        desc="Similar situations or scenarios identified in the context")
    extracted_steps: list[str] = dspy.OutputField(
        desc="Step-by-step procedures extracted from the context that might be relevant")
    applicability_analysis: str = dspy.OutputField(
        desc="Analysis of how applicable these steps are to the original query")
    adaptation_required: bool = dspy.OutputField(
        desc="Whether the extracted steps need adaptation for the specific query")


class InstructionSynthesizer(dspy.Signature):
    """
    You are an expert at synthesizing clear, actionable instructions based on procedural information.
    Your goal is to compose a structured, coherent response that provides tailored guidance
    based EXCLUSIVELY on the retrieved procedural context and the user's specific query.
    You must NEVER use your general knowledge to augment or enhance the instructions.

    When composing instructions:
    1. Organize information in a logical sequence using ONLY information found in the retrieved context
    2. Include any important preconditions or requirements mentioned in the retrieved documents
    3. Present step-by-step guidance when appropriate, based ONLY on the retrieved content
    4. Note any variants or alternatives based on the user's context that are mentioned in the retrieved documents
    5. Ensure coherence by resolving any contradictions in the source material
    6. Avoid duplication or redundancy in steps
    7. Always cite sources with document IDs for all information
    8. If the retrieved information is insufficient to provide complete instructions, clearly acknowledge these limitations
    9. NEVER add steps, preconditions, or information not present in the retrieved documents

    IMPORTANT: If the retrieved information doesn't contain sufficient details to synthesize instructions for the query,
    state clearly that you don't have enough information in the knowledge base. Do NOT attempt to fill in gaps with your general knowledge.
    """

    query: str = dspy.InputField(desc="The user's original question")
    intent: str = dspy.InputField(desc="The extracted intent from the query")
    entities: list[str] = dspy.InputField(desc="The key entities extracted from the query")
    first_hop_results: str = dspy.InputField(desc="The general process information retrieved in the first hop")
    second_hop_results: str = dspy.InputField(desc="The refined information retrieved in the second hop")

    synthesized_instruction: str = dspy.OutputField(desc="The coherent, structured instruction tailored to the query")
    preconditions: list[str] = dspy.OutputField(desc="Any requirements or preconditions that must be met")
    step_by_step_guidance: list[str] = dspy.OutputField(desc="Ordered list of steps or actions to take")
    variants: dict[str, list[str]] = dspy.OutputField(desc="Alternative approaches based on different contexts or roles")
    limitations: list[str] = dspy.OutputField(desc="Limitations or caveats about the instructions provided")
    sources: list[str] = dspy.OutputField(desc="Document sources used for the instruction synthesis")


class LegalRAGResponseGenerator(dspy.Signature):
    """
    You are a helpful, accurate legal assistant using RAG to answer questions.
    When information is provided in your context, use it to give accurate and complete answers based ONLY on that information.
    NEVER use your general knowledge to fill in gaps or enhance answers.

    Follow these steps in order:
    1. Analyze the original query and identify what's being asked
    2. Consider whether the retrieved information directly answers the original question
    3. If not, identify if there are similar situations or applicable procedures in the context
    4. Extract and adapt steps from similar situations when direct answers aren't available
    5. Create a comprehensive response that acknowledges limitations but provides maximum value from ONLY the retrieved information

    IMPORTANT:
    - ONLY answer from the provided context. NEVER use your general knowledge.
    - If the retrieved information doesn't contain sufficient details to answer the question, state clearly that you don't have enough information in the knowledge base.
    - Do NOT attempt to provide answers based on your general knowledge when retrieved information is insufficient.
    - Clearly distinguish between direct answers and adapted guidelines from similar situations.
    - Always cite your sources with document IDs in [brackets] when using retrieved information.
    - When suggesting steps from similar situations, explain the limitations and adaptations needed.
    - Keep your reasoning thorough but make your final answers concise and practical.
    - If no information relevant to the query exists in the retrieved documents, say: "I don't have specific information about this in the retrieved knowledge base."

    Remember: Your response must be derived EXCLUSIVELY from the retrieved documents. Never use information not provided in the context.
    """

    original_query: str = dspy.InputField(desc="The user's original question")
    rearticulated_queries: list[str] = dspy.InputField(
        desc="Alternative ways the question could be expressed")
    retrieved_context: str = dspy.InputField(desc="The retrieved documents from the knowledge base")
    extracted_steps: list = dspy.InputField(
        desc="Any steps extracted from similar situations",
        default=None)

    analysis: str = dspy.OutputField(
        desc="Step-by-step analysis of the information in relation to the query")
    answer: str = dspy.OutputField(
        desc="A helpful answer based ONLY on the retrieved information, including steps from similar situations when relevant")
    sources: list[str] = dspy.OutputField(desc="List of source documents used in the answer")
    query_answered: bool = dspy.OutputField(
        desc="Boolean indicating whether the query was adequately answered based on retrieved information")
    suggested_questions: list[str] = dspy.OutputField(
        desc="List of 2-3 follow-up questions to ask if the information is incomplete")
    similar_situations_used: bool = dspy.OutputField(
        desc="Whether steps or procedures from similar situations were used in the answer")


# Removed SimpleRAGModule - not used in current implementation


class MultiHopRAGModule(dspy.Module):
    """
    DSPy module implementing a multi-hop retrieval process for procedural knowledge.
    Follows proper DSPy patterns with composable modules and optimization support.

    This module implements the architecture outlined in the procedural_genai_multihop_rag_dspy.md:
    1. Extract intent and entities from the query
    2. First-hop retrieval for general process categories
    3. Second-hop retrieval to refine results using identified entities
    4. Synthesize instructions from retrieved knowledge
    """

    def __init__(self, retriever, lm=None, max_results=5, min_confidence=0.3):
        """
        Initialize the MultiHopRAGModule.

        Args:
            retriever: The retriever interface (BedrockKBRetriever instance)
            lm: The language model to use (optional, will use the default if not provided)
            max_results: Maximum number of results to retrieve
            min_confidence: Minimum confidence score threshold
        """
        super().__init__()
        self.retriever = retriever
        self.max_results = max_results
        self.min_confidence = min_confidence
        self.lm = lm

        # Use proper DSPy modules for composability
        self.intent_extractor = IntentExtractionModule()
        self.step_extractor = dspy.ChainOfThought(StepExtractor)
        self.instruction_synthesizer = dspy.ChainOfThought(InstructionSynthesizer)

        # For generating final responses
        self.response_generator = dspy.ChainOfThought(LegalRAGResponseGenerator)

    def forward(self, query):
        """
        Process a query using multi-hop RAG techniques with intent understanding
        and instruction synthesis for procedural knowledge.

        Args:
            query: The user's question

        Returns:
            A prediction object with the tailored response and related metadata
        """
        # Step 1: Extract intent and entities from the query using DSPy module
        with dspy.context(lm=self.lm) if self.lm else dspy.context():
            intent_analysis = self.intent_extractor(query=query)

        # Extract the intent and entities
        intent = intent_analysis.intent
        intent_category = intent_analysis.intent_category
        entities = intent_analysis.entities
        entity_types = intent_analysis.entity_types
        inferred_concepts = intent_analysis.inferred_concepts

        # Step 2: First-hop retrieval - get general process categories or documents
        # Use intent for first retrieval to focus on the right process type
        first_hop_query = f"Process or procedure for {intent}"

        # Add key entities to refine even the first hop
        if entities:
            entity_str = ", ".join(entities[:3])  # Limit to top 3 entities
            first_hop_query += f" related to {entity_str}"

        first_hop_results = self.retriever.retrieve(first_hop_query)

        # Filter by minimum confidence and format
        filtered_first_hop = []
        first_hop_source_docs = []
        first_hop_context_parts = []

        for i, result in enumerate(first_hop_results):
            if result.get('score', 0) >= self.min_confidence:
                filtered_first_hop.append(result)
                document_id = result.get('document_id', f'Document {i + 1}')
                content = result.get('content', '')
                first_hop_context_parts.append(f"[{document_id}]\n{content}")
                first_hop_source_docs.append(document_id)

        first_hop_context = "\n\n".join(first_hop_context_parts)

        # If first hop returned no results, return a generic response
        if not first_hop_context.strip():
            return dspy.Prediction(
                answer=f"I don't have specific information about {intent} in our knowledge base. Could you provide more details about what you're looking for?",
                sources=[],
                intent=intent,
                entities=entities,
                query_answered=False,
                suggested_questions=[
                    f"What specific aspects of {intent} are you interested in?",
                    f"Would you like general information about {intent_category} procedures?",
                    "Can you provide more context about your situation?"
                ]
            )
