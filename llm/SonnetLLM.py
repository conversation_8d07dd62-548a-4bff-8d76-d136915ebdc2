"""
Claude 3.5 Sonnet Integration via AWS Bedrock

This module provides a wrapper for interacting with Claude 3.5 Sonnet model
via AWS Bedrock, specifically designed for RAG applications.

The SonnetLLM class can be used directly or through the ModelPool with
AWSAnthropic integration for better thread safety and resource management.
"""

import boto3
import json
import time
from typing import Any
from llm.rag_processor import RAGProcessor
from llm.error_handling import handle_rag_error, DSPyConfigError
from llm.caching import UnifiedRAGCache
from procleg.logger_config import get_logger

# Get configured logger
logger = get_logger(__name__)



class SonnetLLM:
    """Interface for Claude 3.5 Sonnet through AWS Bedrock."""

    def __init__(
        self,
        model_id: str = "anthropic.claude-3-5-sonnet-20240620-v1:0",
        region_name: str = "us-east-1",
        max_tokens: int = 4096,
        temperature: float = 0.7,
        top_p: float = 0.9,
        system_prompt: str | None = None,
        cache: UnifiedRAGCache | None = None,
        enable_caching: bool = True,
        vectorizer = None
    ):
        """
        Initialize the Claude 3.5 Sonnet LLM integration.

        Args:
            model_id: The model identifier for Claude 3.5 Sonnet
            region_name: AWS region where Bedrock is available
            max_tokens: Maximum number of tokens to generate
            temperature: Temperature for generation (0.0 to 1.0)
            top_p: Top-p sampling parameter (0.0 to 1.0)
            system_prompt: Default system prompt to use for all requests
            cache: Optional UnifiedRAGCache instance for caching responses
            enable_caching: Whether to enable caching of responses
            vectorizer: Optional vectorizer for semantic caching
        """
        self.model_id = model_id
        self.max_tokens = max_tokens
        self.temperature = temperature
        self.top_p = top_p
        self.system_prompt = system_prompt or (
            "You are a helpful, accurate legal and procedural assistant using RAG to answer questions. "
            "You must ONLY use information provided in your retrieved context to answer questions. "
            "NEVER use your general knowledge to fill in gaps - ONLY use what is explicitly provided in the retrieved documents. "
            "\n\n"
            "If the retrieved information doesn't fully address the query, acknowledge this limitation and state clearly "
            "that you don't have sufficient information from the knowledge base to provide a complete answer. "
            "Do NOT attempt to provide answers based on your general knowledge. "
            "\n\n"
            "Always cite your sources with document IDs when using retrieved information. "
            "Keep answers concise, structured, and focused only on information from the retrieved documents. "
            "\n\n"
            "If asked a question where no relevant information is found in the retrieved context, say: "
            '"I don\'t have specific information about this in the retrieved knowledge base." '
            "\n\n"
            "Remember: ONLY respond based on retrieved context. NEVER use your general knowledge."
        )
        self.enable_caching = enable_caching

        # Initialize Bedrock client
        self.bedrock_client = boto3.client(
            'bedrock-runtime',
            region_name=region_name,
        )

        # Initialize RAG processor
        self.rag_processor = RAGProcessor(
            bedrock_client=self.bedrock_client,
            model_id=model_id,
            max_tokens=max_tokens,
            temperature=temperature
        )

        # Initialize or use provided cache
        if enable_caching:
            if cache:
                self.cache = cache
            else:
                # Create a new cache if none provided
                self.cache = UnifiedRAGCache(
                    vectorizer=vectorizer,
                    capacity=1000,  # Default capacity
                    ttl=3600,       # Default TTL (1 hour)
                    enable_semantic_cache=(vectorizer is not None)
                )
                logger.info("Initialized RAG response cache")
        else:
            self.cache = None
            logger.info("Caching disabled for RAG responses")

        logger.info(f"Initialized Claude 3.5 Sonnet LLM with model ID: {model_id}")

    def generate(
        self,
        prompt: str,
        retrieved_context: str | None = None,
        system_prompt: str | None = None,
        temperature: float | None = None,
        max_tokens: int | None = None
    ) -> dict[str, Any]:
        """
        Generate a response from Claude 3.5 Sonnet.

        Args:
            prompt: The user's query
            retrieved_context: Optional retrieved documents to include
            system_prompt: Optional system prompt to override the default
            temperature: Optional temperature override
            max_tokens: Optional max tokens override

        Returns:
            Response from the model with full metadata
        """
        start_time = time.time()

        # Use provided parameters or fall back to defaults
        _system_prompt = system_prompt or self.system_prompt
        _temperature = temperature or self.temperature
        _max_tokens = max_tokens or self.max_tokens

        # Construct the full message payload
        messages = [{"role": "user", "content": prompt}]

        # Set up the messages for Claude 3.5 Sonnet via Bedrock
        if retrieved_context:
            # For Claude on Bedrock, we need to use 'user' and 'assistant' roles
            # System prompt should be incorporated in the request body separately
            messages = [
                {"role": "user", "content": prompt}
            ]
            # Include system prompt and context in the system parameter
            system = f"{_system_prompt}\n\nHere is relevant information for answering the query:\n\n{retrieved_context}"
        else:
            messages = [
                {"role": "user", "content": prompt}
            ]
            # Just use the system prompt
            system = _system_prompt

        # Prepare the request payload
        request_body = {
            "anthropic_version": "bedrock-2023-05-31",
            "system": system,
            "messages": messages,
            "max_tokens": _max_tokens,
            "temperature": _temperature,
            "top_p": self.top_p
        }

        try:
            # Invoke the model
            logger.info("Sending request to Claude 3.5 Sonnet")
            response = self.bedrock_client.invoke_model(
                modelId=self.model_id,
                body=json.dumps(request_body)
            )

            # Parse the response
            response_body = json.loads(response.get('body').read().decode('utf-8'))

            # Extract content
            content = response_body.get('content', [{}])[0].get('text', '')

            # Add timing information
            inference_time = time.time() - start_time
            logger.info(f"Generated response in {inference_time:.2f} seconds")

            # Return enhanced response with metadata
            return {
                "content": content,
                "usage": response_body.get('usage', {}),
                "model": self.model_id,
                "inference_time": inference_time,
                "raw_response": response_body,
                "stop_reason": response_body.get('stop_reason', None)
            }

        except Exception as e:
            logger.error(f"Error generating response from Claude 3.5 Sonnet: {str(e)}")
            raise

    def generate_rag_response(
        self,
        query: str,
        retrieved_results: list[dict[str, Any]],
        format_context_fn=None,
        use_multihop: bool = False,
        **kwargs
    ) -> dict[str, Any]:
        """
        Generate a response using RAG with retrieved results.

        This method implements a retrieval-augmented generation approach, with two options:
        1. Standard RAG using LegalRAGResponseGenerator (when use_multihop=False)
        2. Multi-hop procedural RAG using MultiHopRAGModule (when use_multihop=True)

        The multi-hop approach is particularly effective for procedural queries that require
        step-by-step guidance or understanding of complex processes.

        Args:
            query: The user's query
            retrieved_results: List of retrieved documents with metadata
            format_context_fn: Optional function to format the context
            use_multihop: Whether to use MultiHopRAGModule (True) or EnhancedKnowledgeBaseRAG (False)
            **kwargs: Additional arguments to pass to generate method

        Returns:
            Response from the model with enhanced metadata including:
            - content: The generated response text
            - sources: Source document references
            - model: The model ID used
            - inference_time: Time taken for generation
            - usage: Token usage statistics
            - rag_metadata: Additional metadata about the RAG process

        Note:
            This method includes caching and fallback mechanisms. If a cached response is available,
            it will be returned immediately. If the RAG processing fails, it will fall back to the
            base model with the retrieved context.
        """
        # Start timing for performance metrics
        start_time = time.time()

        # Check cache if enabled
        if self.enable_caching and self.cache:
            cached_response, is_hit, hit_type = self.cache.get(
                query=query,
                use_multihop=use_multihop
            )

            if is_hit:
                # Add cache metadata to the response
                if isinstance(cached_response, dict) and "cache_info" not in cached_response:
                    cached_response["cache_info"] = {
                        "hit_type": hit_type,
                        "cache_time": time.time() - start_time
                    }

                logger.info(f"Cache {hit_type} hit for query: {query[:50]}...")
                return cached_response

        try:
            # Process the query using the RAG processor
            generation_start = time.time()
            rag_response = self.rag_processor.process_query(
                query=query,
                retrieved_results=retrieved_results,
                format_context_fn=format_context_fn,
                use_multihop=use_multihop,
                region_name=kwargs.get("region_name")
            )
            generation_time = time.time() - generation_start

            # If RAG processor returned None (empty context), fall back to base model
            if rag_response is None:
                logger.info("No context available, falling back to base model without context")
                return self.generate(query, **kwargs)

            # If RAG processor encountered an error, handle it with fallback
            if "error" in rag_response:
                # Create a fallback function that generates a response with the context
                def fallback_generator(q, ctx, **kw):
                    return self.generate(
                        prompt=q,
                        retrieved_context=ctx,
                        temperature=kw.get("temperature"),
                        max_tokens=kw.get("max_tokens")
                    )

                # Use the standardized error handling function
                fallback_response = handle_rag_error(
                    error=Exception(rag_response["error"]),
                    query=query,
                    context=rag_response.get("context"),
                    retrieved_results=retrieved_results,
                    fallback_generator=fallback_generator,
                    temperature=kwargs.get("temperature"),
                    max_tokens=kwargs.get("max_tokens")
                )

                # Don't cache fallback responses
                return fallback_response

            # Cache the successful response if caching is enabled
            if self.enable_caching and self.cache:
                self.cache.put(
                    query=query,
                    response=rag_response,
                    retrieved_results=retrieved_results,
                    use_multihop=use_multihop,
                    generation_time=generation_time
                )

            # Return the successful RAG response
            return rag_response

        except DSPyConfigError as e:
            # Handle DSPy configuration errors
            logger.error(f"DSPy configuration error: {str(e)}")
            logger.info("Falling back to base model without DSPy")

            # Generate response without RAG
            response = self.generate(
                prompt=query,
                temperature=kwargs.get("temperature"),
                max_tokens=kwargs.get("max_tokens")
            )

            # Add error information
            response["error_info"] = {
                "error": str(e),
                "error_type": "DSPyConfigError",
                "fallback_used": True
            }

            # Don't cache error responses
            return response

        except Exception as e:
            # Handle any other unexpected errors
            logger.error(f"Unexpected error in generate_rag_response: {str(e)}")

            # Use the standardized error handling with fallback to base model
            fallback_response = handle_rag_error(
                error=e,
                query=query,
                retrieved_results=retrieved_results,
                fallback_generator=lambda q, ctx, **kw: self.generate(
                    prompt=q,
                    temperature=kw.get("temperature"),
                    max_tokens=kw.get("max_tokens")
                ),
                temperature=kwargs.get("temperature"),
                max_tokens=kwargs.get("max_tokens")
            )

            # Don't cache error responses
            return fallback_response

    def get_cache_stats(self) -> dict[str, Any]:
        """
        Get statistics about the RAG response cache.

        Returns:
            Dictionary with cache statistics or None if caching is disabled
        """
        if not self.enable_caching or not self.cache:
            return {"caching_enabled": False}

        return self.cache.get_stats()

    def invalidate_cache(self, query: str | None = None, older_than: int | None = None) -> int:
        """
        Invalidate entries in the RAG response cache.

        Args:
            query: Specific query to invalidate (None for all matching criteria)
            older_than: Invalidate entries older than this many seconds

        Returns:
            Number of entries invalidated or 0 if caching is disabled
        """
        if not self.enable_caching or not self.cache:
            return 0

        return self.cache.invalidate(query=query, older_than=older_than)

    def clear_cache(self) -> None:
        """
        Clear the entire RAG response cache.
        """
        if self.enable_caching and self.cache:
            self.cache.clear()
            logger.info("Cleared RAG response cache")

    @classmethod
    def create_model_pool(
        cls,
        model_id: str = "anthropic.claude-3-5-sonnet-20240620-v1:0",
        region_name: str = "us-east-1",
        max_tokens: int = 4096,
        temperature: float = 0.7,
        top_p: float = 0.9,
        system_prompt: str | None = None,
        pool_size: int = 3
    ):
        """
        Create a model pool for Claude 3.5 Sonnet using AWSAnthropic.

        This method creates a thread-safe model pool for handling concurrent
        requests to Claude 3.5 Sonnet via AWS Bedrock.

        Args:
            model_id: The model identifier for Claude 3.5 Sonnet
            region_name: AWS region where Bedrock is available
            max_tokens: Maximum number of tokens to generate
            temperature: Temperature for generation (0.0 to 1.0)
            top_p: Top-p sampling parameter (0.0 to 1.0)
            system_prompt: Default system prompt to use for all requests
            pool_size: Number of model instances to maintain in the pool

        Returns:
            A ModelPool instance configured for Claude 3.5 Sonnet
        """
        # Import here to avoid circular imports
        from llm.model_pool import ModelPool, ModelConfig

        # Initialize a boto3 client for Bedrock
        bedrock_client = boto3.client('bedrock-runtime', region_name=region_name)

        # Create a model configuration
        model_config = ModelConfig(
            model_id=model_id,
            max_tokens=max_tokens,
            temperature=temperature,
            top_p=top_p,
            system_prompt=system_prompt,
            region_name=region_name
        )

        # Create and return the model pool
        return ModelPool(
            bedrock_client=bedrock_client,
            model_config=model_config,
            pool_size=pool_size
        )
