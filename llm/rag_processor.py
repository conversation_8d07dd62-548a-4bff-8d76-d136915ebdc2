"""
RAG Processor for DSPy-based retrieval-augmented generation.

This module provides a dedicated processor for handling retrieval-augmented
generation using DSPy modules. It encapsulates the DSPy configuration and
RAG processing logic, decoupling it from the LLM interface.
"""

import time
from typing import Any
from collections.abc import Callable
import dspy
from llm.legal_dspy_component import LegalRAGResponseGenerator, MultiHopRAGModule
from llm.dspy_init import configure_dspy_lm, is_dspy_configured, update_dspy_config
from llm.error_handling import handle_rag_error, DSPyConfigError
from procleg.logger_config import get_logger

# Get configured logger
logger = get_logger(__name__)


class RAGProcessor:
    """
    Processor for DSPy-based retrieval-augmented generation.

    This class handles the configuration of DSPy and processing of queries
    using different RAG approaches, including standard RAG and multi-hop RAG.
    """

    def __init__(self, bedrock_client=None, model_id=None, max_tokens=None, temperature=None):
        """
        Initialize the RAG processor.

        Args:
            bedrock_client: AWS Bedrock client
            model_id: The model identifier
            max_tokens: Maximum number of tokens to generate
            temperature: Temperature for generation
        """
        self.bedrock_client = bedrock_client
        self.model_id = model_id
        self.max_tokens = max_tokens
        self.temperature = temperature
        self.dspy_configured = False

    def format_context(self, retrieved_results: list[dict[str, Any]],
                      format_context_fn: Callable | None = None) -> tuple[str, list[str]]:
        """
        Format retrieved results into a context string.

        Args:
            retrieved_results: List of retrieved documents with metadata
            format_context_fn: Optional function to format the context

        Returns:
            Tuple of (formatted_context, source_documents)
        """
        if format_context_fn:
            return format_context_fn(retrieved_results), []

        # Default formatting
        context_parts = []
        source_documents = []
        for i, result in enumerate(retrieved_results):
            content = result.get('content', '')
            document_id = result.get('document_id', f'Document {i+1}')
            context_parts.append(f"[{document_id}]\n{content}")
            source_documents.append(document_id)

        context = "\n\n".join(context_parts)
        return context, source_documents

    def configure_dspy_lm(self, region_name: str | None = None) -> None:
        """
        Configure the DSPy language model if not already set.

        This method uses the centralized DSPy configuration module to ensure
        that DSPy is configured only once per session.

        Args:
            region_name: AWS region name

        Returns:
            None

        Raises:
            DSPyConfigError: If there's an error configuring DSPy
        """
        try:
            # Use the centralized DSPy configuration function
            was_configured = configure_dspy_lm(
                bedrock_client=self.bedrock_client,
                model_id=self.model_id,
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                region_name=region_name or "us-east-1"
            )

            if was_configured:
                self.dspy_configured = True
            else:
                # If DSPy was already configured, check if we need to update parameters
                if is_dspy_configured():
                    # Update the configuration with our parameters if needed
                    update_dspy_config(
                        max_tokens=self.max_tokens,
                        temperature=self.temperature
                    )
                    self.dspy_configured = True
        except DSPyConfigError as e:
            # Re-raise the error to be handled by the caller
            raise e
        except Exception as e:
            # Wrap any other exceptions in a DSPyConfigError
            raise DSPyConfigError(f"Failed to configure DSPy: {str(e)}", original_error=e)

    def create_simple_retriever(self, retrieved_results: list[dict[str, Any]]) -> Any:
        """
        Create a simple retriever class that returns the already retrieved results.

        Args:
            retrieved_results: List of retrieved documents with metadata

        Returns:
            SimpleRetriever instance
        """
        class SimpleRetriever:
            def __init__(self, results):
                self.results = results

            def retrieve(self, query):
                # Simply return the pre-fetched results regardless of the query
                return self.results

        return SimpleRetriever(retrieved_results)

    def process_with_multihop_rag(self, query: str, retrieved_results: list[dict[str, Any]],
                                 source_documents: list[str]) -> tuple[Any, dict[str, Any]]:
        """
        Process a query using the MultiHopRAGModule.

        Args:
            query: The user's query
            retrieved_results: List of retrieved documents with metadata
            source_documents: List of source document IDs

        Returns:
            Tuple of (dspy_response, rag_metadata)
        """
        # Create the multi-hop module with our simple retriever
        retriever_instance = self.create_simple_retriever(retrieved_results)
        rag_module = MultiHopRAGModule(
            retriever=retriever_instance,
            lm=dspy.settings.lm,
            max_results=len(retrieved_results),
            min_confidence=0.0  # Use all provided results
        )

        # Process the query with multi-hop RAG
        dspy_response = rag_module(query=query)

        # Extract any step-by-step guidance if available
        step_by_step = None
        if hasattr(dspy_response, 'step_by_step_guidance') and dspy_response.step_by_step_guidance:
            step_by_step = dspy_response.step_by_step_guidance

        # Get any preconditions if available
        preconditions = None
        if hasattr(dspy_response, 'preconditions') and dspy_response.preconditions:
            preconditions = dspy_response.preconditions

        # Add these to the RAG metadata
        rag_metadata = {
            "num_retrieved": len(retrieved_results),
            "source_documents": source_documents,
            "confidence_scores": [r.get('score', 0) for r in retrieved_results if 'score' in r],
            "step_by_step": step_by_step,
            "preconditions": preconditions
        }

        return dspy_response, rag_metadata

    def process_with_standard_rag(self, query: str, retrieved_results: list[dict[str, Any]],
                                context: str, source_documents: list[str]) -> tuple[Any, dict[str, Any]]:
        """
        Process a query using the LegalRAGResponseGenerator.

        Args:
            query: The user's query
            retrieved_results: List of retrieved documents with metadata
            context: Formatted context string
            source_documents: List of source document IDs

        Returns:
            Tuple of (dspy_response, rag_metadata)
        """
        # Use standard LegalRAGResponseGenerator
        rag_module = dspy.ChainOfThought(LegalRAGResponseGenerator)

        # Use DSPy to generate the response
        dspy_response = rag_module(
            original_query=query,
            detected_language='en',  # Default to English, could be enhanced with language detection
            rearticulated_queries=[],  # Empty list since we're not using rearticulation here
            retrieved_context=context,
            extracted_steps=None  # No extracted steps in this simple implementation
        )

        # Basic RAG metadata
        rag_metadata = {
            "num_retrieved": len(retrieved_results),
            "source_documents": source_documents,
            "confidence_scores": [r.get('score', 0) for r in retrieved_results if 'score' in r]
        }

        return dspy_response, rag_metadata

    def process_query(self, query: str, retrieved_results: list[dict[str, Any]],
                     format_context_fn: Callable | None = None,
                     use_multihop: bool = False,
                     region_name: str | None = None,
                     **kwargs) -> dict[str, Any]:
        """
        Process a query using RAG with retrieved results.

        This method implements a retrieval-augmented generation approach, with two options:
        1. Standard RAG using LegalRAGResponseGenerator (when use_multihop=False)
        2. Multi-hop procedural RAG using MultiHopRAGModule (when use_multihop=True)

        Args:
            query: The user's query
            retrieved_results: List of retrieved documents with metadata
            format_context_fn: Optional function to format the context
            use_multihop: Whether to use MultiHopRAGModule (True) or EnhancedKnowledgeBaseRAG (False)
            region_name: AWS region name
            **kwargs: Additional arguments to pass to the RAG modules

        Returns:
            Dictionary containing the processed response and metadata

        Raises:
            RAGProcessingError: If there's an error processing the RAG query
            DSPyConfigError: If there's an error configuring DSPy
        """
        start_time = time.time()

        # Format the context
        context, source_documents = self.format_context(retrieved_results, format_context_fn)

        # If context is empty, return None to indicate fallback to base model
        if not context:
            return None

        try:
            # Configure the DSPy LM
            self.configure_dspy_lm(region_name)

            # Process with the appropriate RAG module
            if use_multihop:
                dspy_response, rag_metadata = self.process_with_multihop_rag(
                    query, retrieved_results, source_documents
                )
            else:
                dspy_response, rag_metadata = self.process_with_standard_rag(
                    query, retrieved_results, context, source_documents
                )

            # Format the response in the expected structure
            inference_time = time.time() - start_time

            # Get response sources or use source documents if not provided
            sources = dspy_response.sources if hasattr(dspy_response, 'sources') else source_documents

            response = {
                "content": dspy_response.answer,
                "sources": sources,
                "model": self.model_id,
                "inference_time": inference_time,
                "usage": {"input_tokens": 0, "output_tokens": 0},  # Would need token counting logic
                "rag_metadata": rag_metadata,
                "context": context  # Include the context for potential fallback
            }

            return response

        except DSPyConfigError as e:
            # Re-raise DSPy configuration errors to be handled by the caller
            raise e

        except Exception as e:
            # Use the standardized error handling function
            error_info = handle_rag_error(
                error=e,
                query=query,
                context=context,
                retrieved_results=retrieved_results,
                # No fallback generator here - let the caller handle fallbacks
            )

            # Add additional context for the caller
            error_info.update({
                "context": context,
                "source_documents": source_documents,
                "num_retrieved": len(retrieved_results),
                "use_multihop": use_multihop
            })

            return error_info
