"""
Optimized DSPy-based modules for the Legal QA Chatbot RAG application.

This module provides a streamlined DSPy implementation specifically designed for
handling retrieval-augmented generation in the Legal QA Chatbot application.
It implements an optimized multi-hop RAG functionality using the DSPy framework.

The module includes:
1. OptimizedIntentExtractor for fast intent classification with pattern matching
2. OptimizedMultiHopRAGModule that implements an efficient two-stage retrieval process
3. InstructionSynthesizer for composing tailored responses based on procedural context
4. A streamlined KnowledgeBaseChatbot that manages conversation flow

These components work together to provide an efficient multi-hop RAG implementation that
understands user intent, retrieves relevant legal information, and generates accurate,
tailored responses with procedural context for user queries.

Key optimizations:
- Fast pattern-based intent classification for simple queries
- Parallel retrieval for multi-hop processing
- Smart result deduplication
- Removed unused modules (EnhancedRAGModule, SimpleRAGModule, ContextAwareSelfEvaluator)
"""

from typing import Any, Dict, List, Optional
import time
import asyncio
import hashlib
from concurrent.futures import ThreadPoolExecutor

import dspy


class OptimizedIntentExtractor:
    """
    Fast intent classification with pattern matching and caching.
    Uses simple pattern matching for common queries and falls back to LLM for complex cases.
    """

    def __init__(self):
        self.intent_cache = {}
        self.simple_patterns = {
            'definitional': ['what is', 'define', 'meaning of', 'definition', 'explain what'],
            'procedural': ['how to', 'steps', 'process', 'procedure', 'guide', 'instructions'],
            'comparative': ['difference', 'compare', 'versus', 'vs', 'contrast'],
            'requirements': ['requirements', 'needed', 'necessary', 'must have', 'required'],
            'verification': ['allowed', 'permitted', 'valid', 'legal', 'compliance', 'verify']
        }
        # Initialize LLM-based extractor for complex cases
        self.llm_extractor = None

    def _get_content_hash(self, query: str) -> str:
        """Generate a hash for caching purposes."""
        return hashlib.md5(query.lower().strip().encode()).hexdigest()

    def _extract_simple_entities(self, query: str) -> List[str]:
        """Extract basic entities using simple keyword matching."""
        entities = []
        legal_terms = [
            'contract', 'agreement', 'policy', 'regulation', 'statute', 'law',
            'compliance', 'liability', 'intellectual property', 'patent', 'trademark',
            'copyright', 'license', 'vendor', 'client', 'employee', 'data protection'
        ]

        query_lower = query.lower()
        for term in legal_terms:
            if term in query_lower:
                entities.append(term)

        return entities[:5]  # Limit to top 5 entities

    def extract_intent(self, query: str) -> Dict[str, Any]:
        """
        Extract intent and entities from query using fast pattern matching first.
        """
        # Check cache first
        query_hash = self._get_content_hash(query)
        if query_hash in self.intent_cache:
            return self.intent_cache[query_hash]

        # Quick pattern matching first
        query_lower = query.lower()
        for intent_type, patterns in self.simple_patterns.items():
            if any(pattern in query_lower for pattern in patterns):
                result = {
                    'intent': intent_type,
                    'intent_category': intent_type,
                    'confidence': 0.9,
                    'entities': self._extract_simple_entities(query),
                    'entity_types': {},
                    'inferred_concepts': [],
                    'requires_multihop': intent_type in ['procedural', 'requirements'],
                    'method': 'pattern_matching'
                }

                # Cache the result
                if len(self.intent_cache) < 100:  # Limit cache size
                    self.intent_cache[query_hash] = result

                return result

        # Fall back to LLM for complex cases
        return self._llm_intent_extraction(query)

    def _llm_intent_extraction(self, query: str) -> Dict[str, Any]:
        """Fall back to LLM-based intent extraction for complex queries."""
        if self.llm_extractor is None:
            self.llm_extractor = dspy.ChainOfThought(IntentEntityExtractor)

        try:
            with dspy.context():
                intent_analysis = self.llm_extractor(query=query)

            result = {
                'intent': intent_analysis.intent,
                'intent_category': intent_analysis.intent_category,
                'confidence': 0.7,  # Lower confidence for LLM fallback
                'entities': intent_analysis.entities,
                'entity_types': intent_analysis.entity_types,
                'inferred_concepts': intent_analysis.inferred_concepts,
                'requires_multihop': intent_analysis.intent_category in ['procedure', 'requirements'],
                'method': 'llm_extraction'
            }

            # Cache the result
            query_hash = self._get_content_hash(query)
            if len(self.intent_cache) < 100:
                self.intent_cache[query_hash] = result

            return result

        except Exception as e:
            # Fallback to basic classification
            return {
                'intent': 'general_inquiry',
                'intent_category': 'information',
                'confidence': 0.5,
                'entities': self._extract_simple_entities(query),
                'entity_types': {},
                'inferred_concepts': [],
                'requires_multihop': False,
                'method': 'fallback',
                'error': str(e)
            }


class IntentEntityExtractor(dspy.Signature):
    """
    You are an expert at extracting the intent and entities from legal queries.
    Your goal is to accurately identify the primary task or process being asked about (intent)
    and the key contextual entities mentioned in the query.

    For a legal query, the intent might be:
    - Request for procedure (how to do something)
    - Request for information (what is something)
    - Request for clarification (explain something)
    - Request for verification (is something allowed/valid)
    - Request for requirements (what is needed for something)

    Entities are contextual elements such as:
    - Legal concepts (contracts, torts, liability, etc.)
    - Document types (agreement, policy, statute, regulation)
    - Roles or parties (client, vendor, regulator, employee)
    - Conditions or qualifiers (timeframe, jurisdiction, exceptions)
    """
    query: str = dspy.InputField(desc="The user's question")

    intent: str = dspy.OutputField(desc="The primary intent or task type in the query")
    intent_category: str = dspy.OutputField(desc="Category of intent: procedure, information, clarification, verification, or requirements")
    entities: list[str] = dspy.OutputField(desc="List of key entities extracted from the query")
    entity_types: dict[str, str] = dspy.OutputField(desc="Mapping of each entity to its type (concept, document, role, condition)")
    inferred_concepts: list[str] = dspy.OutputField(desc="Additional legal concepts that are implied but not explicitly mentioned")


# Removed unused signature classes:
# - SimpleKBRetriever (not used)
# - QuestionRearticulator (only used in EnhancedRAGModule)
# - ContextualAnalyzer (only used in EnhancedRAGModule)
# - ProcedureAdapter (only used in EnhancedRAGModule)

class StepExtractor(dspy.Signature):
    """
    You are an expert at extracting relevant steps or procedures from similar situations.
    Your goal is to identify procedures, steps, or guidelines that could be applied to the current query.
    """
    query: str = dspy.InputField(desc="The user's question")
    retrieved_context: str = dspy.InputField(desc="The retrieved documents from the knowledge base")

    similar_situations: list[str] = dspy.OutputField(
        desc="Similar situations or scenarios identified in the context")
    extracted_steps: list[str] = dspy.OutputField(
        desc="Step-by-step procedures extracted from the context that might be relevant")
    applicability_analysis: str = dspy.OutputField(
        desc="Analysis of how applicable these steps are to the original query")
    adaptation_required: bool = dspy.OutputField(
        desc="Whether the extracted steps need adaptation for the specific query")


class InstructionSynthesizer(dspy.Signature):
    """
    You are an expert at synthesizing clear, actionable instructions based on procedural information.
    Your goal is to compose a structured, coherent response that provides tailored guidance
    based EXCLUSIVELY on the retrieved procedural context and the user's specific query.
    You must NEVER use your general knowledge to augment or enhance the instructions.

    When composing instructions:
    1. Organize information in a logical sequence using ONLY information found in the retrieved context
    2. Include any important preconditions or requirements mentioned in the retrieved documents
    3. Present step-by-step guidance when appropriate, based ONLY on the retrieved content
    4. Note any variants or alternatives based on the user's context that are mentioned in the retrieved documents
    5. Ensure coherence by resolving any contradictions in the source material
    6. Avoid duplication or redundancy in steps
    7. Always cite sources with document IDs for all information
    8. If the retrieved information is insufficient to provide complete instructions, clearly acknowledge these limitations
    9. NEVER add steps, preconditions, or information not present in the retrieved documents

    IMPORTANT: If the retrieved information doesn't contain sufficient details to synthesize instructions for the query,
    state clearly that you don't have enough information in the knowledge base. Do NOT attempt to fill in gaps with your general knowledge.
    """

    query: str = dspy.InputField(desc="The user's original question")
    intent: str = dspy.InputField(desc="The extracted intent from the query")
    entities: list[str] = dspy.InputField(desc="The key entities extracted from the query")
    first_hop_results: str = dspy.InputField(desc="The general process information retrieved in the first hop")
    second_hop_results: str = dspy.InputField(desc="The refined information retrieved in the second hop")

    synthesized_instruction: str = dspy.OutputField(desc="The coherent, structured instruction tailored to the query")
    preconditions: list[str] = dspy.OutputField(desc="Any requirements or preconditions that must be met")
    step_by_step_guidance: list[str] = dspy.OutputField(desc="Ordered list of steps or actions to take")
    variants: dict[str, list[str]] = dspy.OutputField(desc="Alternative approaches based on different contexts or roles")
    limitations: list[str] = dspy.OutputField(desc="Limitations or caveats about the instructions provided")
    sources: list[str] = dspy.OutputField(desc="Document sources used for the instruction synthesis")


class EnhancedKnowledgeBaseRAG(dspy.Signature):
    """
    You are a helpful, accurate legal assistant using RAG to answer questions.
    When information is provided in your context, use it to give accurate and complete answers based ONLY on that information.
    NEVER use your general knowledge to fill in gaps or enhance answers.

    Follow these steps in order:
    1. Analyze the original query and identify what's being asked
    2. Consider whether the retrieved information directly answers the original question
    3. If not, identify if there are similar situations or applicable procedures in the context
    4. Extract and adapt steps from similar situations when direct answers aren't available
    5. Create a comprehensive response that acknowledges limitations but provides maximum value from ONLY the retrieved information

    IMPORTANT:
    - ONLY answer from the provided context. NEVER use your general knowledge.
    - If the retrieved information doesn't contain sufficient details to answer the question, state clearly that you don't have enough information in the knowledge base.
    - Do NOT attempt to provide answers based on your general knowledge when retrieved information is insufficient.
    - Clearly distinguish between direct answers and adapted guidelines from similar situations.
    - Always cite your sources with document IDs in [brackets] when using retrieved information.
    - When suggesting steps from similar situations, explain the limitations and adaptations needed.
    - Keep your reasoning thorough but make your final answers concise and practical.
    - If no information relevant to the query exists in the retrieved documents, say: "I don't have specific information about this in the retrieved knowledge base."

    Remember: Your response must be derived EXCLUSIVELY from the retrieved documents. Never use information not provided in the context.
    """

    original_query: str = dspy.InputField(desc="The user's original question")
    rearticulated_queries: list[str] = dspy.InputField(
        desc="Alternative ways the question could be expressed")
    retrieved_context: str = dspy.InputField(desc="The retrieved documents from the knowledge base")
    extracted_steps: list = dspy.InputField(
        desc="Any steps extracted from similar situations",
        default=None)

    analysis: str = dspy.OutputField(
        desc="Step-by-step analysis of the information in relation to the query")
    answer: str = dspy.OutputField(
        desc="A helpful answer based ONLY on the retrieved information, including steps from similar situations when relevant")
    sources: list[str] = dspy.OutputField(desc="List of source documents used in the answer")
    query_answered: bool = dspy.OutputField(
        desc="Boolean indicating whether the query was adequately answered based on retrieved information")
    suggested_questions: list[str] = dspy.OutputField(
        desc="List of 2-3 follow-up questions to ask if the information is incomplete")
    similar_situations_used: bool = dspy.OutputField(
        desc="Whether steps or procedures from similar situations were used in the answer")


# Removed SimpleRAGModule - not used in current implementation


class OptimizedMultiHopRAGModule(dspy.Module):
    """
    Optimized DSPy module implementing an efficient multi-hop retrieval process.

    Key optimizations:
    1. Fast pattern-based intent extraction with LLM fallback
    2. Parallel retrieval for multi-hop processing when possible
    3. Smart result deduplication
    4. Skip multi-hop for simple queries

    Process:
    1. Fast intent classification (pattern matching first)
    2. Skip multi-hop for simple definitional queries
    3. Parallel first-hop and second-hop retrieval for complex queries
    4. Smart deduplication and synthesis
    """

    def __init__(self, retriever, lm=None, max_results=5, min_confidence=0.3):
        """
        Initialize the OptimizedMultiHopRAGModule.

        Args:
            retriever: The retriever interface (BedrockKBRetriever instance)
            lm: The language model to use (optional, will use the default if not provided)
            max_results: Maximum number of results to retrieve
            min_confidence: Minimum confidence score threshold
        """
        super().__init__()
        self.retriever = retriever
        self.max_results = max_results
        self.min_confidence = min_confidence
        self.lm = lm

        # Use optimized intent extractor
        self.intent_extractor = OptimizedIntentExtractor()

        # Only initialize what we actually use
        self.step_extractor = dspy.ChainOfThought(StepExtractor)
        self.instruction_synthesizer = dspy.ChainOfThought(InstructionSynthesizer)

        # For simple RAG responses
        self.simple_rag = dspy.ChainOfThought(EnhancedKnowledgeBaseRAG)

    def _get_content_hash(self, content: str) -> str:
        """Generate a hash for deduplication purposes."""
        return hashlib.md5(content[:200].encode()).hexdigest()

    def _merge_and_deduplicate(self, first_hop_results: List[Dict], second_hop_results: List[Dict]) -> List[Dict]:
        """Remove duplicates and merge results intelligently."""
        seen_content_hashes = set()
        seen_doc_ids = set()
        merged_results = []

        # Process first hop results
        for result in first_hop_results:
            doc_id = result.get('document_id', '')
            content_hash = self._get_content_hash(result.get('content', ''))

            if doc_id not in seen_doc_ids and content_hash not in seen_content_hashes:
                seen_doc_ids.add(doc_id)
                seen_content_hashes.add(content_hash)
                result['hop'] = 'first'
                merged_results.append(result)

        # Process second hop results, avoiding duplicates
        for result in second_hop_results:
            doc_id = result.get('document_id', '')
            content_hash = self._get_content_hash(result.get('content', ''))

            if doc_id not in seen_doc_ids and content_hash not in seen_content_hashes:
                seen_doc_ids.add(doc_id)
                seen_content_hashes.add(content_hash)
                result['hop'] = 'second'
                merged_results.append(result)

        # Sort by relevance score
        return sorted(merged_results, key=lambda x: x.get('score', 0), reverse=True)

    def _simple_retrieve_and_respond(self, query: str) -> Any:
        """Handle simple queries that don't need multi-hop processing."""
        # Single retrieval for simple queries
        results = self.retriever.retrieve(query)

        # Filter by confidence
        filtered_results = [r for r in results if r.get('score', 0) >= self.min_confidence]

        if not filtered_results:
            return dspy.Prediction(
                answer="I don't have specific information to answer your question. Could you provide more details or rephrase your question?",
                sources=[],
                query_answered=False,
                method='simple_retrieval'
            )

        # Format context
        context_parts = []
        source_documents = []

        for i, result in enumerate(filtered_results[:self.max_results]):
            content = result.get('content', '')
            document_id = result.get('document_id', f'Document {i + 1}')
            context_parts.append(f"[{document_id}]\n{content}")
            source_documents.append(document_id)

        context = "\n\n".join(context_parts)

        # Generate simple RAG response
        with dspy.context(lm=self.lm) if self.lm else dspy.context():
            response = self.simple_rag(
                original_query=query,
                rearticulated_queries=[],
                retrieved_context=context
            )

        return dspy.Prediction(
            answer=response.answer,
            sources=source_documents,
            query_answered=True,
            method='simple_retrieval'
        )

    def forward(self, query):
        """
        Process a query using optimized multi-hop RAG techniques.

        Args:
            query: The user's question

        Returns:
            A prediction object with the tailored response and related metadata
        """
        # Step 1: Fast intent extraction
        intent_analysis = self.intent_extractor.extract_intent(query)

        # Extract the intent and entities
        intent = intent_analysis['intent']
        intent_category = intent_analysis['intent_category']
        entities = intent_analysis['entities']
        entity_types = intent_analysis.get('entity_types', {})
        inferred_concepts = intent_analysis.get('inferred_concepts', [])
        requires_multihop = intent_analysis.get('requires_multihop', False)

        # Skip multi-hop for simple queries
        if not requires_multihop and intent_category in ['definitional', 'comparative']:
            return self._simple_retrieve_and_respond(query)

        # Step 2: First-hop retrieval - get general process categories or documents
        # Use intent for first retrieval to focus on the right process type
        first_hop_query = f"Process or procedure for {intent}"

        # Add key entities to refine even the first hop
        if entities:
            entity_str = ", ".join(entities[:3])  # Limit to top 3 entities
            first_hop_query += f" related to {entity_str}"

        first_hop_results = self.retriever.retrieve(first_hop_query)

        # Filter by minimum confidence and format
        filtered_first_hop = []
        first_hop_source_docs = []
        first_hop_context_parts = []

        for i, result in enumerate(first_hop_results):
            if result.get('score', 0) >= self.min_confidence:
                filtered_first_hop.append(result)
                document_id = result.get('document_id', f'Document {i + 1}')
                content = result.get('content', '')
                first_hop_context_parts.append(f"[{document_id}]\n{content}")
                first_hop_source_docs.append(document_id)

        first_hop_context = "\n\n".join(first_hop_context_parts)

        # If first hop returned no results, return a generic response
        if not first_hop_context.strip():
            return dspy.Prediction(
                answer=f"I don't have specific information about {intent} in our knowledge base. Could you provide more details about what you're looking for?",
                sources=[],
                intent=intent,
                entities=entities,
                query_answered=False,
                suggested_questions=[
                    f"What specific aspects of {intent} are you interested in?",
                    f"Would you like general information about {intent_category} procedures?",
                    "Can you provide more context about your situation?"
                ]
            )

        # Step 3: Second-hop retrieval - refine results using identified entities and conditions
        # Create a more specific query for the second hop using both intent and entities
        if intent_category == "procedure":
            second_hop_template = "Steps or procedure for {intent} with {entities}"
        elif intent_category == "requirements":
            second_hop_template = "Requirements for {intent} with {entities}"
        elif intent_category == "verification":
            second_hop_template = "Verification process for {intent} with {entities}"
        else:  # information or clarification
            second_hop_template = "Detailed information about {intent} related to {entities}"

        # Add entities and any inferred concepts for a more focused second hop
        if entities:
            entity_str = ", ".join(entities)
            second_hop_query = second_hop_template.format(intent=intent, entities=entity_str)
            if inferred_concepts:
                concept_str = ", ".join(inferred_concepts[:2])  # Limit to top 2 inferred concepts
                second_hop_query += f" considering {concept_str}"
        else:
            # Fallback if no entities were extracted
            second_hop_query = f"Specific details about {intent}"
            if inferred_concepts:
                concept_str = ", ".join(inferred_concepts[:3])
                second_hop_query += f" related to {concept_str}"

        # Execute second hop retrieval
        second_hop_results = self.retriever.retrieve(second_hop_query)

        # Filter and format second hop results
        filtered_second_hop = []
        second_hop_source_docs = []
        second_hop_context_parts = []

        for i, result in enumerate(second_hop_results):
            if result.get('score', 0) >= self.min_confidence:
                # Check for duplicates from first hop
                doc_id = result.get('document_id', '')
                if doc_id and doc_id not in first_hop_source_docs:  # Avoid duplicates
                    filtered_second_hop.append(result)
                    document_id = doc_id or f'Document {i + 1}'
                    content = result.get('content', '')
                    second_hop_context_parts.append(f"[{document_id}]\n{content}")
                    second_hop_source_docs.append(document_id)

        second_hop_context = "\n\n".join(second_hop_context_parts)

        # Combine source documents from both hops
        all_source_docs = first_hop_source_docs + second_hop_source_docs

        # If no second-hop results found, set second_hop_context to empty
        if not second_hop_context.strip():
            second_hop_context = ""

        # Step 4: Extract procedural steps if needed (for procedure intent types)
        if intent_category == "procedure" or intent_category == "requirements":
            # Use all available context for step extraction
            combined_context = first_hop_context
            if second_hop_context.strip():
                combined_context += "\n\n" + second_hop_context

            with dspy.context(lm=self.lm) if self.lm else dspy.context():
                steps_analysis = self.step_extractor(
                    query=query,
                    retrieved_context=combined_context
                )

            similar_situations = steps_analysis.similar_situations if hasattr(steps_analysis, 'similar_situations') else []
            extracted_steps = steps_analysis.extracted_steps if hasattr(steps_analysis, 'extracted_steps') else []
            applicability_analysis = steps_analysis.applicability_analysis if hasattr(steps_analysis, 'applicability_analysis') else ""
        else:
            similar_situations = []
            extracted_steps = []
            applicability_analysis = ""

        # Step 5: Synthesize instructions from retrieved knowledge
        with dspy.context(lm=self.lm) if self.lm else dspy.context():
            instruction = self.instruction_synthesizer(
                query=query,
                intent=intent,
                entities=entities,
                first_hop_results=first_hop_context,
                second_hop_results=second_hop_context
            )

        # Extract synthesized instruction components
        synthesized_instruction = instruction.synthesized_instruction
        preconditions = instruction.preconditions if hasattr(instruction, 'preconditions') else []
        step_by_step_guidance = instruction.step_by_step_guidance if hasattr(instruction, 'step_by_step_guidance') else []
        variants = instruction.variants if hasattr(instruction, 'variants') else {}
        limitations = instruction.limitations if hasattr(instruction, 'limitations') else []
        instruction_sources = instruction.sources if hasattr(instruction, 'sources') else all_source_docs

        # Format the final answer with proper structure
        # Start with the main instruction
        answer = synthesized_instruction

        # Add formatted preconditions if available
        if preconditions:
            answer += "\n\n**Prerequisites:**\n"
            for idx, precond in enumerate(preconditions):
                answer += f"- {precond}\n"

        # Add step-by-step guidance for procedural queries
        if step_by_step_guidance:
            answer += "\n\n**Step-by-Step Procedure:**\n"
            for idx, step in enumerate(step_by_step_guidance):
                answer += f"{idx+1}. {step}\n"

        # Add variants if available
        if variants:
            answer += "\n\n**Variations:**\n"
            for context, variant_steps in variants.items():
                answer += f"For {context}:\n"
                for idx, vstep in enumerate(variant_steps):
                    answer += f"- {vstep}\n"

        # Add limitations and caveats
        if limitations:
            answer += "\n\n**Important Notes:**\n"
            for limitation in limitations:
                answer += f"- {limitation}\n"

        # Add source citations
        if instruction_sources:
            answer += "\n\n**Sources:**\n"
            for source in instruction_sources:
                answer += f"- [{source}]\n"

        # Return comprehensive prediction with all metadata
        return dspy.Prediction(
            answer=answer,
            sources=instruction_sources,
            intent=intent,
            intent_category=intent_category,
            entities=entities,
            entity_types=entity_types,
            inferred_concepts=inferred_concepts,
            preconditions=preconditions,
            step_by_step_guidance=step_by_step_guidance,
            variants=variants,
            limitations=limitations,
            similar_situations=similar_situations,
            extracted_steps=extracted_steps,
            applicability_analysis=applicability_analysis if applicability_analysis else None,
            first_hop_query=first_hop_query,
            second_hop_query=second_hop_query,
            first_hop_sources=first_hop_source_docs,
            second_hop_sources=second_hop_source_docs
        )


# Removed EnhancedRAGModule - not used in current implementation (use_multihop=True)
# Removed ContextAwareSelfEvaluator - enable_self_evaluation is always False


class OptimizedKnowledgeBaseChatbot(dspy.Module):
    """
    Optimized DSPy module for a complete knowledge base chatbot focused on legal and procedural queries.
    This streamlined version uses only the OptimizedMultiHopRAGModule and removes unused complexity.
    """

    def __init__(self, retriever, lm=None, max_results=5, min_confidence=0.3):
        """
        Initialize the OptimizedKnowledgeBaseChatbot.

        Args:
            retriever: The retriever interface
            lm: The language model to use
            max_results: Maximum number of results to retrieve
            min_confidence: Minimum confidence score threshold
        """
        super().__init__()

        # Use only the optimized multi-hop RAG module
        self.rag_module = OptimizedMultiHopRAGModule(
            retriever=retriever,
            lm=lm,
            max_results=max_results,
            min_confidence=min_confidence
        )

        self.conversation_history = []

    def forward(self, query):
        """
        Process a query using the optimized multi-hop RAG module.

        Args:
            query: The user's question

        Returns:
            A prediction object with the answer and sources
        """
        # Direct processing with optimized module - no complex branching
        response = self.rag_module(query=query)

        # Update conversation history
        self.conversation_history.append({
            "role": "user",
            "content": query
        })

        # Create a comprehensive entry for the assistant's response
        assistant_entry = {
            "role": "assistant",
            "content": response.answer,
            "sources": response.sources if hasattr(response, 'sources') else [],
            "timestamp": time.time()
        }

        # Add any procedural knowledge specific metadata if available
        if hasattr(response, 'intent'):
            assistant_entry["intent"] = response.intent
        if hasattr(response, 'step_by_step_guidance') and response.step_by_step_guidance:
            assistant_entry["steps"] = response.step_by_step_guidance
        if hasattr(response, 'preconditions') and response.preconditions:
            assistant_entry["preconditions"] = response.preconditions

        # Add the entry to conversation history
        self.conversation_history.append(assistant_entry)

        return response

    def clear_history(self):
        """Clear the conversation history."""
        self.conversation_history = []


# Maintain backward compatibility by aliasing the optimized version
KnowledgeBaseChatbot = OptimizedKnowledgeBaseChatbot
MultiHopRAGModule = OptimizedMultiHopRAGModule
