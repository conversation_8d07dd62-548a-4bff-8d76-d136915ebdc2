"""
Test script for the DSPy-based Legal QA Chatbot.

This script provides a simple test harness for evaluating the enhanced DSPy
RAG modules with question rearticulation and contextual step extraction capabilities.
"""

import os
import boto3
import dspy
from datetime import datetime
from botocore.config import Config

# Import the DSPy modules
from llm.legal_QA_chatbot_dspy import (
    KnowledgeBaseChatbot,
    # Note: EnhancedRAGModule and SimpleRAGModule have been removed in optimization
    # KnowledgeBaseChatbot now uses OptimizedMultiHopRAGModule internally
)

# Import other required components
from llm.BedrockKBRetriever import BedrockKBRetriever
from llm.SonnetLLM import SonnetLLM
from procleg.logger_config import get_logger

# Get configured logger
logger = get_logger(__name__)

# Constants
DEFAULT_KB_ID = "W3PTE70NR9"  # Replace with your KB ID
DEFAULT_REGION = "us-east-1"  # Replace with your region

# Set up AWS configuration with connection pooling
def get_bedrock_client(region_name):
    """
    Create and configure an AWS Bedrock client with optimized connection settings.
    """
    session = boto3.Session()
    config = Config(
        retries=dict(
            max_attempts=2,
            mode='adaptive'
        ),
        read_timeout=60,
        connect_timeout=60,
        max_pool_connections=50,
        tcp_keepalive=True
    )

    # Create bedrock client with connection pooling
    return session.client(
        service_name='bedrock-runtime',
        region_name=region_name,
        config=config
    )

def initialize_components(kb_id=DEFAULT_KB_ID, region=DEFAULT_REGION,
                          model_id="anthropic.claude-3-5-sonnet-20240620-v1:0",
                          max_tokens=2048, temperature=0.7, system_prompt=None,
                          vector_model="amazon.titan-embed-g1-text-02",
                          enable_self_evaluation=False):
    """
    Initialize all the components needed for the chatbot.
    """
    logger.info("Initializing components...")

    # Initialize AWS clients with connection pooling
    bedrock_client = get_bedrock_client(region)
    agent_client = boto3.client('bedrock-agent-runtime', region_name=region)

    # Initialize the retriever
    retriever = BedrockKBRetriever(
        knowledge_base_id=kb_id,
        region_name=region,
        max_results=5,  # Use a higher number for testing
        min_confidence=0.3
    )

    # Initialize the LLM
    llm = SonnetLLM(
        model_id=model_id,
        region_name=region,
        max_tokens=max_tokens,
        temperature=temperature,
        system_prompt=system_prompt
    )

    # Configure DSPy with the BedRock LLM
    bedrock_lm = dspy.LM(
        model=f"bedrock/{model_id}",
        api_key="not_needed",  # We're using the client directly
        max_tokens=max_tokens,
        temperature=temperature
    )
    dspy.settings.configure(lm=bedrock_lm)

    # Initialize DSPy chatbot
    chatbot = KnowledgeBaseChatbot(
        retriever=retriever,
        lm=bedrock_lm,
        max_results=5,
        min_confidence=0.3,
        enable_self_evaluation=enable_self_evaluation
    )

    return {
        "retriever": retriever,
        "llm": llm,
        "bedrock_client": bedrock_client,
        "agent_client": agent_client,
        "dspy_chatbot": chatbot,
        "dspy_lm": bedrock_lm
    }

def test_enhanced_rag():
    """
    Test the enhanced RAG capabilities with question rearticulation and step extraction.
    """
    print("Initializing components for testing...")

    # Try to get components, but handle failures gracefully for local testing
    try:
        components = initialize_components(enable_self_evaluation=True)
        chatbot = components["dspy_chatbot"]
        print("✅ Successfully initialized all components with AWS Bedrock")
    except Exception as e:
        print(f"⚠️ Error initializing with AWS: {str(e)}")
        print("Continuing with mock components for testing the code flow only...")

        # Create a mock retriever for testing without AWS credentials
        class MockRetriever:
            def retrieve(self, query):
                print(f"Mock retriever called with query: {query}")
                return [
                    {
                        "document_id": "doc1",
                        "content": "This is a sample legal document discussing compliance requirements.",
                        "score": 0.95
                    },
                    {
                        "document_id": "doc2",
                        "content": "Here are steps for filing a legal document: 1. Complete all forms. 2. Sign documents. 3. Submit to court.",
                        "score": 0.85
                    }
                ]

        # Create a simple mock module for testing
        chatbot = EnhancedRAGModule(retriever=MockRetriever())

    # Test queries
    test_queries = [
        "What are the steps for filing a legal document?",
        "What compliance requirements do we need to follow?",
        "How do I handle a situation where a contract is breached?",
        "What's the process for appealing a decision?"
    ]

    for query in test_queries:
        print(f"\n\n=== Testing Query: '{query}' ===")
        try:
            # Process the query
            response = chatbot(query=query)

            # Display the results
            print(f"\nAnswer: {response.answer}")
            print(f"\nSources: {response.sources}")

            # Show the rearticulated queries
            if hasattr(response, 'rearticulated_queries'):
                print(f"\nRearticulated Queries: {response.rearticulated_queries}")

            # Show the core intent
            if hasattr(response, 'core_intent'):
                print(f"\nCore Intent: {response.core_intent}")

            # Show any extracted steps
            if hasattr(response, 'extracted_steps') and response.extracted_steps:
                print(f"\nExtracted Steps: {response.extracted_steps}")

            # Show any similar situations identified
            if hasattr(response, 'similar_situations') and response.similar_situations:
                print(f"\nSimilar Situations: {response.similar_situations}")

            # Show context relevance assessment
            if hasattr(response, 'context_relevance'):
                print(f"\nContext Relevance: {response.context_relevance}")

            # Show identified gaps
            if hasattr(response, 'identified_gaps') and response.identified_gaps:
                print(f"\nIdentified Gaps: {response.identified_gaps}")

            # Show evaluation results if available
            if hasattr(response, 'evaluation'):
                eval_result = response.evaluation
                print("\n=== Self-Evaluation Results ===")
                print(f"Faithfulness Score: {eval_result.faithfulness_score}/5")
                print(f"Faithfulness Feedback: {eval_result.faithfulness_feedback}")
                print(f"Relevance Score: {eval_result.relevance_score}/5")
                print(f"Relevance Feedback: {eval_result.relevance_feedback}")
                print(f"Completeness Score: {eval_result.completeness_score}/5")
                print(f"Completeness Feedback: {eval_result.completeness_feedback}")
                print("\nSuggested Improvements:")
                for suggestion in eval_result.suggested_improvements:
                    print(f"- {suggestion}")

        except Exception as e:
            print(f"Error processing query: {str(e)}")
            import traceback
            traceback.print_exc()

def test_simple_dspy_chatbot():
    """Test the original simple DSPy chatbot implementation."""
    try:
        # Configuration
        kb_id = DEFAULT_KB_ID
        region_name = DEFAULT_REGION
        model_id = "anthropic.claude-3-5-sonnet-20240620-v1:0"
        max_tokens = 2048
        temperature = 0.7
        max_results = 3
        min_confidence = 0.3

        # Initialize Bedrock client
        boto3.client('bedrock-runtime', region_name=region_name)

        # Initialize retriever
        retriever = BedrockKBRetriever(
            knowledge_base_id=kb_id,
            region_name=region_name,
            max_results=max_results,
            min_confidence=min_confidence
        )

        # Configure DSPy with Bedrock LLM
        bedrock_lm = dspy.LM(
            model=f"bedrock/{model_id}",
            api_key="not_needed",  # We're using the client directly
            max_tokens=max_tokens,
            temperature=temperature
        )
        dspy.settings.configure(lm=bedrock_lm)
        logger.info("Configured DSPy with Bedrock LLM")

        # Initialize DSPy chatbot
        chatbot = KnowledgeBaseChatbot(
            retriever=retriever,
            lm=bedrock_lm,
            max_results=max_results,
            min_confidence=min_confidence
        )
        logger.info("Initialized DSPy chatbot")

        # Test query
        query = "What are the key legal considerations for a software license agreement?"
        logger.info(f"Testing query: {query}")

        # Generate response using DSPy chatbot
        response = chatbot(query=query)

        # Print response
        logger.info("Response generated successfully")
        logger.info(f"Answer: {response.answer}")
        logger.info(f"Sources: {response.sources}")

        return True
    except Exception as e:
        logger.error(f"Error testing DSPy chatbot: {str(e)}")
        return False

if __name__ == "__main__":
    # Choose which test to run
    test_enhanced_rag()
    # Or run the original simple test
    # test_simple_dspy_chatbot()