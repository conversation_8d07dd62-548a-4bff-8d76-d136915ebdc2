import os
import re
import html
import streamlit as st
import procleg.frontend.utils.styles_loader as styles_loader
import uuid
import dspy
import time
import boto3
from datetime import datetime
from collections import defaultdict
import markdown
import streamlit.components.v1 as components
from langchain_core.messages import HumanMessage, AIMessage

from procleg.frontend.components.legal_qa_header import render_legal_qa_header
from procleg.backend.chatbot.conversation_thread import (
    load_conversations,
    save_conversation,
    load_conversations_metadata_only
)
# Import AWS Bedrock and GenAI components
from botocore.config import Config
from llm.BedrockKBRetriever import BedrockKBRetriever
from llm.SonnetLLM import SonnetLLM
from llm.model_pool import ModelPool, ModelConfig
from llm.legal_dspy_component import KnowledgeBaseChatbot
from llm.caching import SimpleVectorizer
from services.initialization import initialization_state
from oracletool.base.vectorizers import BedrockVectorizer
from procleg.logger_config import get_logger
from dotenv import load_dotenv

load_dotenv()
# Configure logging
logger = get_logger(__name__)
KNOWLEDGE_BASE_ID = os.getenv("BEDROCK_KNOWLEDGE_BASE_ID","W3PTE70NR9") # Replace with your KB ID
AWS_REGION=os.getenv("AWS_REGION", "us-east-1")
BEDROCK_MODEL_ID = os.getenv("BEDROCK_MODEL_ID", "anthropic.claude-3-5-sonnet-20240620-v1:0")

def should_reload_legal_conversations(prid):
    """
    Determine if legal conversations should be reloaded based on cache state and user changes.

    Args:
        prid (str): Current user's project ID

    Returns:
        bool: True if conversations should be reloaded
    """
    # Check if user changed
    if st.session_state.get('legal_current_prid') != prid:
        st.session_state.legal_current_prid = prid
        return True

    # Check if forced reload is requested
    if st.session_state.get('legal_reload_conversations', False):
        return True

    # Check if cache exists for this user
    cache_key = f"{prid}_legal"
    if cache_key not in st.session_state.get('legal_conversation_metadata_cache', {}):
        return True

    # Check cache age (reload after 5 minutes)
    import time
    cache_timestamp = st.session_state.get('legal_conversation_cache_timestamp', {}).get(cache_key, 0)
    if time.time() - cache_timestamp > 300:  # 5 minutes
        return True

    return False

def load_legal_conversations_smart(prid, force_reload=False):
    """
    Smart conversation loading that uses metadata-only loading and caching for legal module.

    Args:
        prid (str): Project ID to load conversations for
        force_reload (bool): Force reload even if cache exists

    Returns:
        list: List of conversation metadata
    """
    cache_key = f"{prid}_legal"

    # Initialize cache if not exists
    if 'legal_conversation_metadata_cache' not in st.session_state:
        st.session_state.legal_conversation_metadata_cache = {}
    if 'legal_conversation_cache_timestamp' not in st.session_state:
        st.session_state.legal_conversation_cache_timestamp = {}

    # Check if we should use cached data
    if not force_reload and not should_reload_legal_conversations(prid):
        return st.session_state.legal_conversation_metadata_cache[cache_key]

    try:
        # Load metadata-only for fast performance
        conversations, metrics = load_conversations_metadata_only(prid, 'legal')

        # Update cache
        import time
        st.session_state.legal_conversation_metadata_cache[cache_key] = conversations
        st.session_state.legal_conversation_cache_timestamp[cache_key] = time.time()
        st.session_state.legal_reload_conversations = False

        # Log performance for monitoring
        logger.info(f"Loaded {len(conversations)} legal conversation metadata in {metrics.total_time:.2f}s "
                   f"(Cache hits: {metrics.cache_hits}, S3 time: {metrics.s3_fetch_time:.2f}s)")

        return conversations

    except Exception as e:
        logger.error(f"Error loading legal conversations: {e}")
        # Return empty list on error
        return []

def load_legal_conversation_content(prid, thread_id):
    """
    Load full conversation content for a specific legal thread.

    Args:
        prid (str): Project ID
        thread_id (str): Thread ID to load content for

    Returns:
        dict: Full conversation data with messages
    """
    try:
        # Load all conversations to get the specific thread content
        conversations = load_conversations(prid, 'legal')

        for conversation in conversations:
            if conversation.get('thread_id') == thread_id:
                return conversation

        return None

    except Exception as e:
        logger.error(f"Error loading legal conversation content for thread {thread_id}: {e}")
        return None
def render():
    """Render the legal Q&A chat interface."""
    styles_loader.load_css(page_css="legal_qa.css")

    # Initialize session state variables
    if "legal_chat_history" not in st.session_state:
        st.session_state.legal_chat_history = []

    if "legal_chat_active" not in st.session_state:
        st.session_state.legal_chat_active = False

    if 'legal_thread_id' not in st.session_state:
        st.session_state['legal_thread_id'] = str(uuid.uuid4())

    if "legal_retrieved_context" not in st.session_state:
        st.session_state.legal_retrieved_context = []

    if "legal_processing_complete" not in st.session_state:
        st.session_state.legal_processing_complete = False

    # Initialize cache for semantic similarity search
    if "legal_response_cache" not in st.session_state:
        st.session_state.legal_response_cache = {}

    # Enable caching by default
    if "legal_enable_caching" not in st.session_state:
        st.session_state.legal_enable_caching = True

    # Initialize all conversation-related state variables to avoid KeyErrors
    if "legal_conversations" not in st.session_state:
        st.session_state.legal_conversations = []

    if "legal_convos_by_date" not in st.session_state:
        st.session_state.legal_convos_by_date = {}

    if "legal_conversations_loaded" not in st.session_state:
        st.session_state.legal_conversations_loaded = False

    # Initialize conversation cache management for legal module
    if 'legal_conversation_metadata_cache' not in st.session_state:
        st.session_state.legal_conversation_metadata_cache = {}

    if 'legal_conversation_cache_timestamp' not in st.session_state:
        st.session_state.legal_conversation_cache_timestamp = {}

    if 'legal_current_prid' not in st.session_state:
        st.session_state.legal_current_prid = None

    if 'legal_reload_conversations' not in st.session_state:
        st.session_state.legal_reload_conversations = True

    if "legal_message_obj" not in st.session_state:
        st.session_state.legal_message_obj = {}

    if "legal_retrieved_docs" not in st.session_state:
        st.session_state.legal_retrieved_docs = []

    # Define show_context with default value True to display retrieved context

    prid = st.session_state["user"]["prid"]

    # Add a small UI enhancement for feedback on chat quality (optional feature)
    if "show_feedback_ui" not in st.session_state:
        st.session_state.show_feedback_ui = False

    # Initialize AWS Bedrock and GenAI components
    initialization_state()

    # Default Knowledge Base settings
    DEFAULT_SYSTEM_PROMPT = """You are a helpful, accurate legal and procedural assistant using RAG to answer questions in multiple languages.
    You must ONLY use information provided in your retrieved context to answer questions.
    NEVER use your general knowledge to fill in gaps - ONLY use what is explicitly provided in the retrieved documents.

    CRITICAL LANGUAGE REQUIREMENT:
    - ALWAYS respond in the SAME LANGUAGE as the user's original query
    - If the user asks in Spanish, respond in Spanish
    - If the user asks in French, respond in French
    - If the user asks in German, respond in German
    - If the user asks in English, respond in English
    - Maintain the same language throughout your entire response

    Follow these steps for each query:
    1. First, identify the language of the user's query
    2. Analyze step-by-step whether the retrieved information directly addresses the user's query
    3. If the information fully answers the query, provide a concise response based ONLY on that information (in the user's language)
    4. If the information partially answers the query, clearly state what aspects are addressed and what is missing (in the user's language)
    5. If the information doesn't address the query at all, acknowledge this and suggest specific follow-up questions (in the user's language)
    6. For procedural queries, organize information in a step-by-step format when appropriate

    IMPORTANT RULES:
    - ONLY respond based on retrieved context. NEVER use your general knowledge.
    - ALWAYS respond in the same language as the user's query
    - If the retrieved information doesn't fully address the query, acknowledge this limitation and state clearly
      that you don't have sufficient information from the knowledge base to provide a complete answer (in the user's language).
    - Always cite your sources with document IDs in [brackets] when using retrieved information
    - If information is missing, suggest 2-3 specific follow-up questions that would be more answerable (in the user's language)
    - Keep your reasoning thorough but make your final answers concise and direct
    - When information is found in multiple sources, synthesize it clearly
    - Present step-by-step guidance for procedural questions when available in the retrieved documents

    Remember: Your response must be derived EXCLUSIVELY from the retrieved documents AND must be in the same language as the user's query."""

    # Get bedrock client with connection pooling
    def get_bedrock_client(region_name):
        """
        Create and configure an AWS Bedrock client with optimized connection settings.

        This function creates a boto3 client for the Bedrock runtime service with
        connection pooling and retry settings optimized for production use.

        Args:
            region_name (str): AWS region where Bedrock is available

        Returns:
            boto3.client: Configured Bedrock runtime client with optimized settings
        """
        session = boto3.Session()
        config = Config(
            retries=dict(
                max_attempts=2,
                mode='adaptive'
            ),
            read_timeout=60,
            connect_timeout=60,
            max_pool_connections=50,
            tcp_keepalive=True
        )

        # Create bedrock client with connection pooling
        return session.client(
            service_name='bedrock-runtime',
            region_name=region_name,
            config=config
        )

    # Initialize components on first load
    @st.cache_resource
    def initialize_legal_components(kb_id, region, model_id, max_tokens, temperature, system_prompt):
        """
        Initialize and cache all the components needed for the legal chatbot.

        This function creates and configures all the necessary components for the chatbot,
        including AWS clients, retriever, LLM, model pool, vectorizers, and DSPy modules.
        It uses Streamlit's cache_resource decorator to ensure components are only
        initialized once and reused across sessions for better performance.

        Args:
            kb_id (str): Knowledge Base ID for the Bedrock KB retriever
            region (str): AWS region for Bedrock services
            model_id (str): Claude model ID to use
            max_tokens (int): Maximum tokens for response generation
            temperature (float): Temperature setting for response generation (0.0-1.0)
            system_prompt (str): System prompt to use for the LLM

        Returns:
            dict: Dictionary containing all initialized components
        """
        logger.info("Initializing legal chatbot components...")

        # Initialize AWS clients with connection pooling
        bedrock_client = get_bedrock_client(region)
        boto3.client('bedrock-agent-runtime', region_name=region)

        # Initialize the retriever
        retriever = BedrockKBRetriever(
            knowledge_base_id=kb_id,
            region_name=region,
            max_results=3,
            min_confidence=0.3
        )

        # Initialize model pools for better performance
        sonnet_config = ModelConfig(
            model_id=model_id,
            max_tokens=max_tokens,
            temperature=temperature
        )

        # Create a model pool with 3 instances for concurrent queries
        model_pool = ModelPool(bedrock_client, sonnet_config, pool_size=3)

        # Initialize the LLM
        llm = SonnetLLM(
            model_id=model_id,
            region_name=region,
            max_tokens=max_tokens,
            temperature=temperature,
            system_prompt=system_prompt
        )

        # Configure DSPy with the BedRock LLM
        bedrock_lm = dspy.LM(
            model=f"bedrock/{model_id}",
            api_key="not_needed",  # We're using the client directly
            max_tokens=max_tokens,
            temperature=temperature
        )
        dspy.settings.configure(lm=bedrock_lm)

        # Initialize DSPy chatbot with multi-hop RAG for procedural knowledge
        chatbot = KnowledgeBaseChatbot(
            retriever=retriever,
            lm=bedrock_lm,
            max_results=3,
            min_confidence=0.3,
            use_multihop=True  # Enable multi-hop RAG for better procedural knowledge handling
        )

        logger.info("Initialized Legal QA chatbot with multi-hop RAG capabilities")

        # Initialize vectorizer for embedding generation
        vector_model = "amazon.titan-embed-g1-text-02"
        vectorizer = BedrockVectorizer(
            modelId=vector_model,
            api_type="G1"
        )

        # Initialize simple vectorizer for caching
        cache_vectorizer = SimpleVectorizer(
            bedrock_client=bedrock_client,
            model_id=vector_model
        )

        return {
            "retriever": retriever,
            "llm": llm,
            "model_pool": model_pool,
            "bedrock_client": bedrock_client,
            "dspy_chatbot": chatbot,
            "dspy_lm": bedrock_lm,
            "vectorizer": vectorizer,
            "cache_vectorizer": cache_vectorizer
        }

    # Initialize components
    legal_components = initialize_legal_components(
        KNOWLEDGE_BASE_ID, AWS_REGION, BEDROCK_MODEL_ID, 2048, 0.1, DEFAULT_SYSTEM_PROMPT
    )

    render_legal_qa_header()

    legalQAContainer = st.container(key="legal_qa_container")
    with legalQAContainer:
        historyColumn, chatBotColumn = st.columns([1, 4])

        # Load conversations using smart caching - always refresh to get latest data
        try:
            # Use smart loading with metadata-only for fast performance
            conversations = load_legal_conversations_smart(prid)

            # Pre-process conversations for faster rendering
            convos_by_date = defaultdict(list)
            for convo in conversations:
                date_str = convo.get("createdAt", "")[:10]
                try:
                    date_obj = datetime.fromisoformat(date_str)
                    date_label = date_obj.strftime("%b %d, %Y")
                except Exception:
                    date_label = date_str or "Unknown Date"
                convos_by_date[date_label].append(convo)

            # Update session state with loaded data
            st.session_state.legal_conversations = conversations
            st.session_state.legal_convos_by_date = convos_by_date
            st.session_state.legal_conversations_loaded = True
        except Exception as e:
            logger.error(f"Error loading legal conversations: {str(e)}")
            # Set empty defaults on error
            st.session_state.legal_conversations = []
            st.session_state.legal_convos_by_date = {}
            st.session_state.legal_conversations_loaded = True

    # History column - now shows immediately while data loads in background
    with historyColumn:
        historyContainer = st.container(key="legal_history_container")
        with historyContainer:
            col_header, col_button = st.columns([3, 1])
            with col_header:
                st.markdown('<h4 style="margin-bottom: 0.5rem; margin-top: -0.8rem; font-weight: bold;">Legal Chats</h4>', unsafe_allow_html=True)
            with col_button:
                with st.container():
                    st.markdown('<div class="st-key-new_btn">', unsafe_allow_html=True)
                    if st.button("New", key="new_legal_chat_btn"):
                        st.session_state['legal_chat_history'] = []
                        st.session_state['legal_thread_id'] = str(uuid.uuid4())
                        st.session_state['legal_chat_active'] = False
                        # No need to reload conversations for new chat - metadata doesn't change
                    st.markdown('</div>', unsafe_allow_html=True)

            # No need to check if loaded - we've already set up empty defaults
            # Show most recent dates first
            if st.session_state.legal_convos_by_date:
                for date_label in sorted(st.session_state.legal_convos_by_date.keys(), reverse=True):
                    with st.expander(date_label):
                        for conversation in st.session_state.legal_convos_by_date[date_label]:
                            thread_name = conversation.get("thread_name", "Untitled")
                            thread_id = conversation.get('thread_id', 'unknown')

                            # Show message count for metadata-only conversations
                            message_count = conversation.get('message_count', 0)
                            display_name = f"{thread_name} ({message_count} msgs)" if message_count > 0 else thread_name

                            if st.button(display_name, key=f"legal_thread_{thread_id}"):
                                st.session_state['legal_chat_history'] = []
                                st.session_state['legal_thread_id'] = thread_id
                                st.session_state['legal_chat_active'] = True

                                # Lazy load conversation content only when needed
                                conversation_content = load_legal_conversation_content(prid, thread_id)
                                if conversation_content and "text" in conversation_content:
                                    for message in conversation_content["text"]:
                                        role = message["role"]
                                        content = message["content"]
                                        st.session_state['legal_chat_history'].append({'role': role, 'content': content})

                                # No need to reload conversation list - we're just switching threads
            else:
                # Show message if no conversations found
                st.info("No previous conversations found. Start a new chat below.")

        # Chatbot column - Always render the chat container immediately
        with chatBotColumn:
            chatContainer = st.container(key="legal_chat_container")
            with chatContainer:
                # Use a placeholder for chat content that will be filled regardless of history loading
                st.empty()

                if not st.session_state.legal_chat_active:
                    # Simple welcome screen
                    st.markdown(
                        """
                        <div style="text-align: center; padding: 20px; font-weight: bold; font-size: 30px; color: #1c2262; margin-top: 2rem; margin-bottom: 9rem;">
                        Welcome to the Legal Q&A Assistant Page.
                        </div>
                        """,
                        unsafe_allow_html=True
                    )
                else:
                    chat_html = """
                    <style>
                        .chat-row {
                            display: flex;
                            align-items: flex-start;
                            margin-bottom: 24px;
                        }
                        .user-row {
                            flex-direction: row;
                        }
                        .ai-row {
                            flex-direction: row;
                        }
                        .avatar {
                            width: 44px;
                            height: 44px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: bold;
                            font-size: 1.1em;
                            margin-right: 18px;
                            margin-left: 8px;
                        }
                        .user-avatar {
                            background: #e0e0e0;
                            color: #222;
                            border-radius: 6px;
                        }
                        .ai-avatar {
                            background: #20b8c7;
                            color: #fff;
                            border-radius: 6px;
                        }
                        .chat-bubble {
                            padding: 18px 32px;
                            border-radius: 32px;
                            font-size: 1.08em;
                            background: #fff;
                            border: 2px solid #eee;
                            width: 100%;
                            min-width: 0;
                            word-break: break-word;
                            box-sizing: border-box;
                            user-select: text;
                            -webkit-user-select: text;
                            -moz-user-select: text;
                            -ms-user-select: text;
                        }
                        .user-bubble {
                            border: 2px solid #ff9800;
                            color: #222;
                        }
                        .ai-bubble {
                            border: 2px solid #20b8c7;
                            color: #222;
                            position: relative;
                        }
                        .copy-button {
                            position: absolute;
                            bottom: 10px;
                            right: 18px;
                            background: none;
                            border: none;
                            cursor: pointer;
                            padding: 5px;
                            opacity: 0.6;
                            transition: opacity 0.2s;
                        }
                        .copy-button:hover {
                            opacity: 1;
                        }
                        .copy-button svg {
                            width: 16px;
                            height: 16px;
                            fill: #212627;
                        }
                        .copy-tooltip {
                            position: absolute;
                            top: -25px;
                            right: 0;
                            background: #333;
                            color: white;
                            padding: 4px 8px;
                            border-radius: 4px;
                            font-size: 12px;
                            opacity: 0;
                            transition: opacity 0.2s;
                            pointer-events: none;
                        }
                        .copy-tooltip.show {
                            opacity: 1;
                        }

                        .custom-scrollbar::-webkit-scrollbar {
                            width: 6px;
                        }
                        .custom-scrollbar::-webkit-scrollbar-track {
                            background: transparent;
                        }
                        .custom-scrollbar::-webkit-scrollbar-thumb {
                            background-color: rgba(0, 0, 0, 0.2);
                            border-radius: 10px;
                        }

                        /* Ensure text selection works everywhere */
                        * {
                            -webkit-user-select: text !important;
                            -moz-user-select: text !important;
                            -ms-user-select: text !important;
                            user-select: text !important;
                        }

                        /* Allow text selection on all text elements */
                        p, div, span, h1, h2, h3, h4, h5, h6, li, td, th {
                            -webkit-user-select: text !important;
                            -moz-user-select: text !important;
                            -ms-user-select: text !important;
                            user-select: text !important;
                        }

                        /* Prevent selection on buttons and interactive elements only */
                        button, .copy-btn {
                            -webkit-user-select: none !important;
                            -moz-user-select: none !important;
                            -ms-user-select: none !important;
                            user-select: none !important;
                        }
                    </style>
                    <div id="chat-scroll" class="custom-scrollbar" style="padding: 50px; background: #fff; height: calc(100vh - 250px); min-height: 467px; overflow-y: auto; overflow-x: hidden; scroll-behavior: auto; margin-left: -4rem; margin-top: -3rem; user-select: text; -webkit-user-select: text; -moz-user-select: text; -ms-user-select: text;">
                        <div style="display: flex; flex-direction: column;">
                    """

                    # Building messages inside the chat-section
                    user_initials = "SG"
                    if "user_name" in st.session_state:
                        parts = st.session_state["user_name"].split()
                        if len(parts) > 1:
                            user_initials = f"{parts[0][0]}{parts[1][0]}".upper()
                        else:
                            user_initials = parts[0][0].upper()

                    for msg in st.session_state.legal_chat_history:
                        if hasattr(msg, 'additional_kwargs') and msg.additional_kwargs.get("role"):
                            role = msg.additional_kwargs.get("role")
                            content_raw = msg.content
                        elif isinstance(msg, dict) and "role" in msg and "content" in msg:
                            role = msg["role"]
                            content_raw = msg["content"]
                        else:
                            continue

                        if role == "user":
                            chat_html += f"""
                            <div class="chat-row user-row">
                                <div class="avatar user-avatar">{user_initials}</div>
                                <div class="chat-bubble user-bubble">{content_raw}</div>
                            </div>
                            """
                        else:
                            # Process AI responses with enhanced markdown support
                            # Format AI response for better readability - apply proper code and list formatting

                            # 1. Pre-process the content for better formatting
                            # Log the content transformation for debugging
                            logger.info(f"Processing AI response with length: {len(content_raw)}")

                            # Here content_raw is the raw text from the AI response
                            # We'll create content_formatted with enhanced formatting
                            content_formatted = content_raw

                            # Process code blocks correctly
                            code_block_pattern = r'```([a-zA-Z]*)(\r?\n([\s\S]*?)\r?\n)```'
                            content_formatted = re.sub(code_block_pattern, lambda m:
                                f'<div class="code-block"><div class="code-header">{m.group(1) or "code"}</div>' +
                                f'<pre><code>{m.group(3)}</code></pre></div>',
                                content_formatted)

                            # 2. Convert to HTML with extended markdown support
                            try:
                                # The content_formatted variable holds the pre-processed content
                                # It needs to be converted to HTML with markdown
                                logger.info(f"Converting markdown content with length: {len(content_formatted)}")
                                logger.info(f"Markdown content sample before conversion: {content_formatted[:200]}...")

                                # Use the markdown library to convert markdown to HTML
                                content = markdown.markdown(
                                    content_formatted,
                                    extensions=[
                                        'fenced_code',  # Better code blocks
                                        'codehilite',  # Code syntax highlighting
                                        'tables',      # Markdown tables support
                                        'nl2br',       # Convert newlines to <br>
                                        'sane_lists'   # Better list handling
                                    ]
                                )

                                # Ensure there's content even if markdown conversion fails
                                if not content.strip():
                                    # Fallback to simple HTML formatting but preserve markdown formatting
                                    logger.warning("Markdown produced empty content, using fallback with markdown formatting")

                                    # Process basic markdown manually to preserve formatting
                                    content_with_formatting = content_raw
                                    # Convert **text** to <strong>text</strong>
                                    content_with_formatting = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', content_with_formatting)
                                    # Convert *text* to <em>text</em>
                                    content_with_formatting = re.sub(r'\*(.*?)\*', r'<em>\1</em>', content_with_formatting)
                                    # Convert # headers
                                    content_with_formatting = re.sub(r'^# (.*?)$', r'<h1>\1</h1>', content_with_formatting, flags=re.MULTILINE)
                                    content_with_formatting = re.sub(r'^## (.*?)$', r'<h2>\1</h2>', content_with_formatting, flags=re.MULTILINE)
                                    content_with_formatting = re.sub(r'^### (.*?)$', r'<h3>\1</h3>', content_with_formatting, flags=re.MULTILINE)

                                    # Process lists
                                    # First, identify list blocks
                                    list_pattern = r'((?:^- .*$\n?)+)'
                                    numbered_list_pattern = r'((?:^\d+\. .*$\n?)+)'

                                    # Process unordered lists
                                    def replace_ul(match):
                                        list_text = match.group(1)
                                        items = re.findall(r'^- (.*)$', list_text, re.MULTILINE)
                                        list_html = '<ul style="margin-left: 20px; margin-top: 10px; margin-bottom: 10px;">'
                                        for item in items:
                                            list_html += f'<li style="margin-bottom: 5px;">{item}</li>'
                                        list_html += '</ul>'
                                        return list_html

                                    # Process ordered lists
                                    def replace_ol(match):
                                        list_text = match.group(1)
                                        items = re.findall(r'^\d+\. (.*)$', list_text, re.MULTILINE)
                                        list_html = '<ol style="margin-left: 20px; margin-top: 10px; margin-bottom: 10px;">'
                                        for item in items:
                                            list_html += f'<li style="margin-bottom: 5px;">{item}</li>'
                                        list_html += '</ol>'
                                        return list_html

                                    # Apply list replacements
                                    content_with_formatting = re.sub(list_pattern, replace_ul, content_with_formatting, flags=re.MULTILINE)
                                    content_with_formatting = re.sub(numbered_list_pattern, replace_ol, content_with_formatting, flags=re.MULTILINE)

                                    # Convert remaining newlines to <br>
                                    content_with_formatting = content_with_formatting.replace('\n', '<br>')

                                    content = f"<div>{content_with_formatting}</div>"
                                else:
                                    # Log the conversion results for debugging
                                    logger.info(f"Markdown converted content length: {len(content)}")
                                    content_sample = content[:200] + "..." if len(content) > 200 else content
                                    logger.info(f"Markdown content sample after conversion: {content_sample}")
                            except Exception as e:
                                logger.error(f"Markdown conversion error: {str(e)}")
                                # Fallback to simple HTML with basic markdown formatting preserved

                                # Process basic markdown manually to preserve formatting
                                content_with_formatting = content_raw
                                # Convert **text** to <strong>text</strong>
                                content_with_formatting = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', content_with_formatting)
                                # Convert *text* to <em>text</em>
                                content_with_formatting = re.sub(r'\*(.*?)\*', r'<em>\1</em>', content_with_formatting)
                                # Convert # headers
                                content_with_formatting = re.sub(r'^# (.*?)$', r'<h1>\1</h1>', content_with_formatting, flags=re.MULTILINE)
                                content_with_formatting = re.sub(r'^## (.*?)$', r'<h2>\1</h2>', content_with_formatting, flags=re.MULTILINE)
                                content_with_formatting = re.sub(r'^### (.*?)$', r'<h3>\1</h3>', content_with_formatting, flags=re.MULTILINE)

                                # Process lists
                                # First, identify list blocks
                                list_pattern = r'((?:^- .*$\n?)+)'
                                numbered_list_pattern = r'((?:^\d+\. .*$\n?)+)'

                                # Process unordered lists
                                def replace_ul(match):
                                    list_text = match.group(1)
                                    items = re.findall(r'^- (.*)$', list_text, re.MULTILINE)
                                    list_html = '<ul style="margin-left: 20px; margin-top: 10px; margin-bottom: 10px;">'
                                    for item in items:
                                        list_html += f'<li style="margin-bottom: 5px;">{item}</li>'
                                    list_html += '</ul>'
                                    return list_html

                                # Process ordered lists
                                def replace_ol(match):
                                    list_text = match.group(1)
                                    items = re.findall(r'^\d+\. (.*)$', list_text, re.MULTILINE)
                                    list_html = '<ol style="margin-left: 20px; margin-top: 10px; margin-bottom: 10px;">'
                                    for item in items:
                                        list_html += f'<li style="margin-bottom: 5px;">{item}</li>'
                                    list_html += '</ol>'
                                    return list_html

                                # Apply list replacements
                                content_with_formatting = re.sub(list_pattern, replace_ul, content_with_formatting, flags=re.MULTILINE)
                                content_with_formatting = re.sub(numbered_list_pattern, replace_ol, content_with_formatting, flags=re.MULTILINE)

                                # Convert remaining newlines to <br>
                                content_with_formatting = content_with_formatting.replace('\n', '<br>')

                                content = f"<div>{content_with_formatting}</div>"
                                logger.warning("Using manually formatted HTML content due to markdown conversion error")

                            # 3. Apply additional formatting for improved readability
                            # Add better styling for lists
                            content = content.replace('<ul>', '<ul style="margin-left: 20px; margin-top: 10px; margin-bottom: 10px;">')
                            content = content.replace('<ol>', '<ol style="margin-left: 20px; margin-top: 10px; margin-bottom: 10px;">')
                            content = content.replace('<li>', '<li style="margin-bottom: 5px;">')

                            # Better paragraph spacing
                            content = content.replace('<p>', '<p style="margin-bottom: 15px;">')

                            # Add better table styling
                            content = content.replace('<table>', '<table style="border-collapse: collapse; width: 100%; margin: 15px 0;">')
                            content = content.replace('<th>', '<th style="border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2; text-align: left;">')
                            content = content.replace('<td>', '<td style="border: 1px solid #ddd; padding: 8px;">')

                            # 4. Check if the message contains source citations in [doc_id] format and highlight them
                            source_pattern = r'\[(Document \d+|[A-Za-z0-9_\-\.]+)\]'
                            content = re.sub(source_pattern, r'<span style="background-color: #e8f4f8; padding: 0 4px; border-radius: 3px; color: #0277bd; font-weight: 500;">\g<0></span>', content)

                            # Further highlight specific docs from retrieved results
                            for doc in st.session_state.legal_retrieved_docs if "legal_retrieved_docs" in st.session_state else []:
                                doc_id = doc.get('document_id', '')
                                if doc_id and f"[{doc_id}]" in content:
                                    # Add tooltip with relevance info for document references
                                    score = doc.get('score', 0)
                                    relevance = "High" if score > 0.8 else "Medium" if score > 0.5 else "Low"
                                    content = content.replace(
                                        f'<span style="background-color: #e8f4f8; padding: 0 4px; border-radius: 3px; color: #0277bd; font-weight: 500;">[{doc_id}]</span>',
                                        f'<span style="background-color: #e8f4f8; padding: 0 4px; border-radius: 3px; color: #0277bd; font-weight: 500;" title="Relevance: {relevance} ({score:.2f})">[{doc_id}]</span>'
                                    )

                            # Check if this is the latest AI message and we have sources to display
                            sources_html = ""
                            if len(st.session_state.legal_chat_history) > 0 and msg == st.session_state.legal_chat_history[-1] and role == "assistant":
                                if "legal_message_obj" in st.session_state and "legal_retrieved_docs" in st.session_state and st.session_state.legal_processing_complete:
                                    # Generate sources HTML with improved formatting and metadata
                                    # Initialize sources_html with a proper container structure
                                    sources_html = """
                                    <div class="sources-container" style="margin-top: 15px; border-top: 1px solid #eee; padding-top: 10px; margin-bottom: 10px;">
                                        <div style="font-weight: bold; margin-bottom: 8px; color: #1c2262;">Sources:</div>
                                        <div style="font-size: 0.85em; color: #777; margin-bottom: 12px;">These sources were used to generate the response. Click each source to view the content.</div>
                                        <div class="sources-list">
                                    """

                                    # Add sources from retrieved_docs with enhanced presentation
                                    if st.session_state.legal_retrieved_docs:
                                        # Sort sources by relevance score (highest first)
                                        sorted_docs = sorted(st.session_state.legal_retrieved_docs, key=lambda x: x.get('score', 0), reverse=True)

                                        for i, doc in enumerate(sorted_docs):
                                            doc_id = doc.get('document_id', f"Document {i+1}")
                                            score = doc.get('score', 0)
                                            relevance_category = doc.get('relevance_category', 'medium')

                                            # Calculate a color based on the relevance category
                                            if relevance_category == 'high':
                                                color = "#2e7d32"  # Darker green for high relevance
                                                relevance_label = "High Relevance"
                                            elif relevance_category == 'medium':
                                                color = "#f9a825"  # Gold/amber for medium relevance
                                                relevance_label = "Medium Relevance"
                                            else:
                                                color = "#757575"  # Gray for low relevance
                                                relevance_label = "Low Relevance"

                                            # Format score as percentage
                                            min(100, max(0, int(score * 100)))

                                            # Get content without truncation
                                            content = doc.get('content', '')

                                            # Get metadata if available
                                            metadata = doc.get('metadata', {})
                                            metadata_html = ""

                                            if metadata:
                                                metadata_html = "<div style='margin-top: 8px; font-size: 0.85em; color: #666;'>"
                                                for key, value in metadata.items():
                                                    if key != 'text' and value:  # Skip the text field and empty values
                                                        metadata_html += f"<div><strong>{key}:</strong> {value}</div>"
                                                metadata_html += "</div>"

                                            sources_html += f"""
                                            <details class="source-item" style="margin-bottom: 10px; padding: 8px; border-radius: 6px; background-color: rgba(240, 240, 240, 0.3); border-left: 3px solid {color};">
                                                <summary style="font-weight: 500; color: #333; cursor: pointer; padding: 5px 0; display: flex; justify-content: space-between; align-items: center;">
                                                    <div>
                                                        <span style="font-weight: 600;">{doc_id}</span>
                                                    </div>
                                                    <div>
                                                        <span style="display: inline-block; padding: 2px 8px; border-radius: 4px; background-color: {color}; color: white; font-size: 0.8em;">{relevance_label} ({score:.2f})</span>
                                                    </div>
                                                </summary>
                                                <div style="margin-top: 10px; font-size: 0.9em; color: #555; border-top: 1px solid #eee; padding-top: 10px;">
                                                    {metadata_html}
                                                    <div style="margin-top: 8px;">{content}</div>
                                                </div>
                                            </details>
                                            """

                                    # Close the sources container properly
                                    sources_html += """
                                        </div>
                                    </div>
                                    """

                                    # Set about_html to empty string (removing the about section)

                                    # Reset the flag after rendering
                                    st.session_state.legal_processing_complete = False

                            # Add response metadata and styling for better presentation
                            metadata_html = ""

                            # Add metadata bar with cache info and timing
                            metadata_parts = []

                            # Add cache indicator if response was from cache
                            if "legal_message_obj" in st.session_state and st.session_state.legal_message_obj.get("response", {}).get("from_cache", False):
                                metadata_parts.append("<span title='Retrieved from semantic cache'>🔄 Cached response</span>")

                            # Add timing information
                            search_time = st.session_state.legal_message_obj.get("response", {}).get("search_time", 0) if "legal_message_obj" in st.session_state else 0
                            if search_time > 0:
                                metadata_parts.append(f"<span title='Total processing time'>⏱️ {search_time:.2f}s</span>")

                            # Add source count if available
                            if "legal_retrieved_docs" in st.session_state and st.session_state.legal_retrieved_docs:
                                source_count = len(st.session_state.legal_retrieved_docs)
                                metadata_parts.append(f"<span title='Number of sources referenced'>📚 {source_count} sources</span>")

                            if metadata_parts:
                                metadata_html = f"<div style='display: flex; gap: 15px; font-size: 0.8em; color: #666; margin-bottom: 8px; justify-content: flex-end;'>{' '.join(metadata_parts)}</div>"

                            # Add improved styling for AI response
                            ai_bubble_style = """
                            <style>
                                .ai-bubble {
                                    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
                                    line-height: 1.5;
                                }
                                .ai-content * {
                                    margin-bottom: 10px !important;
                                }
                                .ai-content p {
                                    margin-bottom: 15px !important;
                                    margin-top: 0;
                                }
                                .ai-content h1, .ai-content h2, .ai-content h3, .ai-content h4 {
                                    margin-top: 20px !important;
                                    margin-bottom: 10px !important;
                                    font-weight: 600;
                                    color: #1c2262;
                                }
                                .ai-content ul, .ai-content ol {
                                    margin-left: 20px !important;
                                    margin-top: 10px !important;
                                    margin-bottom: 10px !important;
                                    padding-left: 20px !important;
                                }
                                .ai-content li {
                                    margin-bottom: 5px !important;
                                }
                                .ai-content pre {
                                    background-color: #f5f7f9;
                                    border-radius: 5px;
                                    padding: 12px;
                                    overflow-x: auto;
                                    margin: 15px 0;
                                    border-left: 3px solid #20b8c7;
                                }
                                .ai-content code {
                                    font-family: 'SF Mono', SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
                                    font-size: 0.9em;
                                    background-color: #f0f0f0;
                                    padding: 2px 4px;
                                    border-radius: 3px;
                                }
                                .ai-content blockquote {
                                    border-left: 3px solid #ddd;
                                    padding-left: 15px;
                                    margin-left: 0;
                                    color: #555;
                                    font-style: italic;
                                }
                                .code-block {
                                    margin: 15px 0;
                                    border-radius: 5px;
                                    overflow: hidden;
                                }
                                .code-header {
                                    background-color: #e0e0e0;
                                    padding: 5px 10px;
                                    font-family: monospace;
                                    font-size: 0.8em;
                                    color: #333;
                                    border-top-left-radius: 5px;
                                    border-top-right-radius: 5px;
                                    text-transform: lowercase;
                                }
                                /* Fix for empty boxes */
                                .ai-content br {
                                    display: block;
                                    content: "";
                                    margin-top: 5px;
                                }
                                /* Ensure proper rendering */
                                .ai-content > *:first-child {
                                    margin-top: 0 !important;
                                }
                            </style>
                            """

                            metadata_html = ai_bubble_style + metadata_html

                            # Create the about HTML section only for the last message
                            # About section has been removed

                            # Render the AI message with improved styling
                            # Fix the broken HTML by ensuring the proper div structure
                            # For debugging, log the content before and after processing
                            logger.info(f"Content raw length: {len(content_raw)}")
                            logger.info(f"Content processed length: {len(content)}")

                            # Use content_raw for both the copy button and display if there's a significant difference
                            # This ensures consistency between what's copied and what's displayed
                            display_content = content

                            # Calculate a proportional threshold based on content length
                            # For short content, use a smaller absolute threshold
                            # For longer content, use a percentage-based threshold
                            content_length = len(content_raw)
                            if content_length < 500:
                                threshold = 50  # Smaller absolute threshold for short content
                            else:
                                threshold = max(100, int(content_length * 0.1))  # 10% of content length or at least 100 chars

                            # Log the threshold being used for debugging
                            logger.info(f"Using content difference threshold: {threshold} chars (content length: {content_length})")

                            # Check if the difference exceeds the threshold
                            content_diff = abs(len(content) - len(content_raw))
                            if content_diff > threshold:
                                logger.warning(f"Significant difference in content length: raw={len(content_raw)}, processed={len(content)}, diff={content_diff} chars")

                                # Log a sample of both contents for debugging if the difference is very large
                                if content_diff > threshold * 2:  # If difference is more than double the threshold
                                    logger.debug(f"Raw content sample: {content_raw[:100]}...")
                                    logger.debug(f"Processed content sample: {content[:100]}...")

                                # Process content_raw with basic markdown formatting to preserve structure
                                content_with_formatting = content_raw
                                # Convert **text** to <strong>text</strong>
                                content_with_formatting = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', content_with_formatting)
                                # Convert *text* to <em>text</em>
                                content_with_formatting = re.sub(r'\*(.*?)\*', r'<em>\1</em>', content_with_formatting)
                                # Convert # headers
                                content_with_formatting = re.sub(r'^# (.*?)$', r'<h1>\1</h1>', content_with_formatting, flags=re.MULTILINE)
                                content_with_formatting = re.sub(r'^## (.*?)$', r'<h2>\1</h2>', content_with_formatting, flags=re.MULTILINE)
                                content_with_formatting = re.sub(r'^### (.*?)$', r'<h3>\1</h3>', content_with_formatting, flags=re.MULTILINE)

                                # Process lists
                                # First, identify list blocks
                                list_pattern = r'((?:^- .*$\n?)+)'
                                numbered_list_pattern = r'((?:^\d+\. .*$\n?)+)'

                                # Process unordered lists
                                def replace_ul(match):
                                    list_text = match.group(1)
                                    items = re.findall(r'^- (.*)$', list_text, re.MULTILINE)
                                    list_html = '<ul style="margin-left: 20px; margin-top: 10px; margin-bottom: 10px;">'
                                    for item in items:
                                        list_html += f'<li style="margin-bottom: 5px;">{item}</li>'
                                    list_html += '</ul>'
                                    return list_html

                                # Process ordered lists
                                def replace_ol(match):
                                    list_text = match.group(1)
                                    items = re.findall(r'^\d+\. (.*)$', list_text, re.MULTILINE)
                                    list_html = '<ol style="margin-left: 20px; margin-top: 10px; margin-bottom: 10px;">'
                                    for item in items:
                                        list_html += f'<li style="margin-bottom: 5px;">{item}</li>'
                                    list_html += '</ol>'
                                    return list_html

                                # Apply list replacements
                                content_with_formatting = re.sub(list_pattern, replace_ul, content_with_formatting, flags=re.MULTILINE)
                                content_with_formatting = re.sub(numbered_list_pattern, replace_ol, content_with_formatting, flags=re.MULTILINE)

                                # Highlight source citations in [doc_id] format
                                source_pattern = r'\[(Document \d+|[A-Za-z0-9_\-\.]+)\]'
                                content_with_formatting = re.sub(source_pattern, r'<span style="background-color: #e8f4f8; padding: 0 4px; border-radius: 3px; color: #0277bd; font-weight: 500;">\g<0></span>', content_with_formatting)

                                # Further highlight specific docs from retrieved results
                                for doc in st.session_state.legal_retrieved_docs if "legal_retrieved_docs" in st.session_state else []:
                                    doc_id = doc.get('document_id', '')
                                    if doc_id and f"[{doc_id}]" in content_with_formatting:
                                        # Add tooltip with relevance info for document references
                                        score = doc.get('score', 0)
                                        relevance = "High" if score > 0.8 else "Medium" if score > 0.5 else "Low"
                                        content_with_formatting = content_with_formatting.replace(
                                            f'<span style="background-color: #e8f4f8; padding: 0 4px; border-radius: 3px; color: #0277bd; font-weight: 500;">[{doc_id}]</span>',
                                            f'<span style="background-color: #e8f4f8; padding: 0 4px; border-radius: 3px; color: #0277bd; font-weight: 500;" title="Relevance: {relevance} ({score:.2f})">[{doc_id}]</span>'
                                        )

                                # Convert remaining newlines to <br>
                                content_with_formatting = content_with_formatting.replace('\n', '<br>')

                                # Use the manually formatted content
                                display_content = f"<div class='manually-formatted'>{content_with_formatting}</div>"
                                logger.info("Using manually formatted raw content for display due to significant processing differences")

                            chat_html += f"""
                            <div class="chat-row ai-row">
                                <div class="avatar ai-avatar">AI</div>
                                <div class="chat-bubble ai-bubble">
                                    {metadata_html}
                                    <button class="copy-button" onclick="copyToClipboard(this)" data-copy-text="{html.escape(content_raw)}">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                            <path d="M16 1H4C2.9 1 2 1.9 2 3v14h2V3h12V1zm3 4H8C6.9 5 6 5.9 6 7v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                                        </svg>
                                    </button>
                                    <div class="copy-tooltip">Copy to clipboard</div>
                                    <div class="ai-content" style="line-height: 1.5; font-size: 1em;">{display_content}</div>
                                    {sources_html}
                                </div>
                            </div>
                            """

                            # Removed incorrect closing tags that were breaking the layout

                    chat_html += """
                        </div>
                    </div>
                    <script>
                      // Controlled scroll behavior that doesn't interfere with text selection
                      let isUserScrolling = false;
                      let scrollTimeout;

                      // Track user scrolling to avoid auto-scroll during manual scrolling
                      function trackUserScroll() {
                          const chatScroll = document.getElementById('chat-scroll');
                          if (chatScroll) {
                              chatScroll.addEventListener('scroll', function() {
                                  isUserScrolling = true;
                                  clearTimeout(scrollTimeout);
                                  scrollTimeout = setTimeout(() => {
                                      isUserScrolling = false;
                                  }, 2000); // Stop considering user scrolling after 2 seconds
                              });
                          }
                      }

                      // Helper function to scroll to bottom only when appropriate
                      function scrollToBottomIfNeeded() {
                          if (isUserScrolling) return; // Don't auto-scroll if user is manually scrolling

                          const chatScroll = document.getElementById('chat-scroll');
                          if (chatScroll) {
                              const isNearBottom = chatScroll.scrollTop + chatScroll.clientHeight >= chatScroll.scrollHeight - 100;
                              if (isNearBottom) {
                                  chatScroll.scrollTop = chatScroll.scrollHeight;
                              }
                          }
                      }

                      // Initialize scroll tracking and do initial scroll
                      document.addEventListener('DOMContentLoaded', function() {
                          trackUserScroll();
                          // Only do initial scroll, no aggressive repeated scrolling
                          setTimeout(scrollToBottomIfNeeded, 300);
                      });

                      function copyToClipboard(button) {
                        // Get text from data attribute to avoid JavaScript escaping issues
                        const text = button.getAttribute('data-copy-text');
                        if (!text) {
                            showCopyFeedback(button, false);
                            return;
                        }

                        // Use modern Clipboard API if available, fallback to legacy method
                        if (navigator.clipboard && window.isSecureContext) {
                            navigator.clipboard.writeText(text).then(() => {
                                showCopyFeedback(button, true);
                            }).catch((err) => {
                                console.error('Clipboard API failed:', err);
                                fallbackCopy(button, text);
                            });
                        } else {
                            fallbackCopy(button, text);
                        }
                      }

                      function fallbackCopy(button, text) {
                        const textarea = document.createElement('textarea');
                        textarea.value = text;
                        textarea.style.position = 'fixed';
                        textarea.style.opacity = '0';
                        document.body.appendChild(textarea);

                        textarea.select();
                        textarea.setSelectionRange(0, 99999); // For mobile devices

                        try {
                            const successful = document.execCommand('copy');
                            showCopyFeedback(button, successful);
                        } catch (err) {
                            showCopyFeedback(button, false);
                        }

                        document.body.removeChild(textarea);
                      }

                      function showCopyFeedback(button, success) {
                        const tooltip = button.nextElementSibling;
                        tooltip.textContent = success ? 'Copied!' : 'Copy failed';
                        tooltip.classList.add('show');

                        setTimeout(() => {
                            tooltip.classList.remove('show');
                            setTimeout(() => {
                                tooltip.textContent = 'Copy to clipboard';
                            }, 200);
                        }, 2000);
                      }
                    </script>
                    """

                    st.components.v1.html(chat_html, height=590)


                if "user_message" not in st.session_state:
                    st.session_state.user_message = ""

                def process_message(user_input):
                    """Process a user message and generate AI response"""
                    # Set chat as active
                    st.session_state.legal_chat_active = True

                    # Add user message to chat history
                    st.session_state.legal_chat_history.append(
                        HumanMessage(content=user_input, additional_kwargs={"role": "user"})
                    )

                    # Prepare chat message for saving
                    chat_message = []
                    chat_message.append({'role': "user", 'content': user_input})

                    return chat_message

                def send_message():
                    """Button callback to send a message"""
                    if st.session_state.user_input:
                        # Store user input in session state to process outside callback
                        st.session_state.pending_user_input = st.session_state.user_input
                        # Clear input field immediately
                        st.session_state.user_input = ""

                # Check if there's a pending message to process (outside the callback)
                if "pending_user_input" in st.session_state and st.session_state.pending_user_input:
                    user_input = st.session_state.pending_user_input
                    # Clear pending input to prevent reprocessing
                    st.session_state.pending_user_input = ""

                    # Process the message
                    chat_message = process_message(user_input)
                    logger.info(f"Processing user message with thread_id: {st.session_state['legal_thread_id']}")

                    try:
                        # Save the user message immediately to ensure it's not lost
                        save_result = save_conversation(chat_message, prid, st.session_state['legal_thread_id'], "legal")
                        if not save_result:
                            logger.warning("Failed to save user message - will retry after AI response")

                        # Variables for tracking results and time
                        response = None
                        retrieved_results = []
                        retrieve_time = 0
                        generate_time = 0
                        from_cache = False

                        # Check if response is in cache using semantic similarity search
                        if st.session_state.legal_enable_caching and "cache_vectorizer" in legal_components:
                            cache_vectorizer = legal_components["cache_vectorizer"]

                            # Get embedding for current query
                            query_embedding = cache_vectorizer(user_input)

                            # Check cache for similar questions
                            for cached_query, cached_data in st.session_state.legal_response_cache.items():
                                cached_embedding = cached_data.get("embedding", None)

                                if cached_embedding is not None:
                                    # Calculate similarity between current query and cached queries
                                    similarity = SimpleVectorizer.calculate_similarity(query_embedding, cached_embedding)

                                    # If similarity exceeds threshold (0.95), use cached response
                                    if similarity > 0.95:  # High similarity threshold
                                        logger.info(f"Cache hit with similarity: {similarity:.4f}")
                                        ai_response = cached_data.get("response")
                                        retrieved_results = cached_data.get("retrieved_results", [])
                                        from_cache = True
                                        break

                        # If no cache hit, perform the full RAG process
                        if not from_cache:
                            # Step 1: Retrieve relevant information from Bedrock Knowledge Base
                            retrieve_start = time.time()
                            retriever = legal_components["retriever"]

                            # Log the retrieval process
                            logger.info(f"Retrieving documents for query: {user_input[:50]}...")

                            # Get raw retrieval results
                            retrieved_results = retriever.retrieve(user_input)
                            logger.info(f"Raw retrieval results: {retrieved_results}")
                            retrieve_time = time.time() - retrieve_start
                            logger.info(f"Retrieved {len(retrieved_results)} documents in {retrieve_time:.2f}s")

                            # Process and enhance the retrieved results
                            enhanced_results = []
                            for i, result in enumerate(retrieved_results):
                                # Get the original data
                                score = result.get('score', 0)
                                content = result.get('content', '')
                                doc_id = result.get('document_id', f'Document {i+1}')
                                metadata = result.get('metadata', {})

                                # Filter out results with very low confidence
                                if score < 0.1:  # Filter very low confidence results
                                    logger.info(f"Filtering out low confidence result: {doc_id} (score: {score:.2f})")
                                    continue

                                # For scores that pass the filter, normalize and enhance
                                # Normalize confidence scores to be more intuitive (higher is better)
                                normalized_score = min(1.0, max(0.0, score))

                                # Create enriched result
                                enhanced_result = {
                                    'score': normalized_score,
                                    'document_id': doc_id,
                                    'content': content,
                                    'metadata': metadata,
                                    # Add additional context-related fields
                                    'relevance_category': 'high' if normalized_score > 0.8 else 'medium' if normalized_score > 0.5 else 'low',
                                }

                                # Add the enhanced result to our list
                                enhanced_results.append(enhanced_result)

                            # Sort by score (highest first)
                            enhanced_results.sort(key=lambda x: x['score'], reverse=True)

                            # Use the enhanced results
                            retrieved_results = enhanced_results

                            # Save retrieved context in session state
                            st.session_state.legal_retrieved_context = retrieved_results

                            # Step 2: Format the retrieved context for better RAG
                            if retrieved_results:
                                context_parts = []
                                for i, result in enumerate(retrieved_results):
                                    # Format each retrieved document with clear source attribution
                                    doc_id = result.get('document_id', f'Document {i+1}')
                                    content = result.get('content', '')
                                    score = result.get('score', 0)

                                    # Format with clear document boundaries and metadata
                                    context_part = f"SOURCE [{doc_id}] (Relevance: {score:.2f})\n{content}\n"
                                    context_parts.append(context_part)

                                # Join all context parts with clear separation
                                "\n----------\n".join(context_parts)
                                logger.info(f"Formatted context with {len(context_parts)} documents")

                            # Step 3: Generate response with RAG
                            generate_start = time.time()
                            logger.info("Generating response...")

                            # Use DSPy chatbot for structured RAG
                            if "dspy_chatbot" in legal_components:
                                try:
                                    dspy_chatbot = legal_components["dspy_chatbot"]

                                    # Clear history for a fresh conversation
                                    dspy_chatbot.clear_history()

                                    # Generate response using DSPy chatbot with improved context and reasoning
                                    dspy_response = dspy_chatbot(query=user_input)
                                    logger.info(f"DSPy retrieval results: {dspy_response}")
                                except Exception as dspy_error:
                                    logger.error(f"Error with DSPy chatbot: {str(dspy_error)}")
                                    logger.info("Falling back to SonnetLLM due to DSPy error")
                                    # Set flag to use fallback
                                    dspy_response = None

                                # Extract reasoning analysis
                                analysis = dspy_response.analysis if hasattr(dspy_response, 'analysis') else ""
                                query_answered = dspy_response.query_answered if hasattr(dspy_response, 'query_answered') else True

                                # Log the reasoning analysis
                                if analysis:
                                    logger.info(f"Reasoning analysis: {analysis}")
                                logger.info(f"Query sufficiently answered: {query_answered}")

                                # Extract all enhanced data if available
                                suggested_questions = dspy_response.suggested_questions if hasattr(dspy_response, 'suggested_questions') else []
                                similar_situations_used = dspy_response.similar_situations_used if hasattr(dspy_response, 'similar_situations_used') else False
                                rearticulated_queries = dspy_response.rearticulated_queries if hasattr(dspy_response, 'rearticulated_queries') else []
                                similar_situations = dspy_response.similar_situations if hasattr(dspy_response, 'similar_situations') else []
                                extracted_steps = dspy_response.extracted_steps if hasattr(dspy_response, 'extracted_steps') else []

                                # Extract procedural knowledge specific elements (from multi-hop RAG)
                                intent = dspy_response.intent if hasattr(dspy_response, 'intent') else None
                                intent_category = dspy_response.intent_category if hasattr(dspy_response, 'intent_category') else None
                                entities = dspy_response.entities if hasattr(dspy_response, 'entities') else []
                                preconditions = dspy_response.preconditions if hasattr(dspy_response, 'preconditions') else []
                                step_by_step_guidance = dspy_response.step_by_step_guidance if hasattr(dspy_response, 'step_by_step_guidance') else []
                                variants = dspy_response.variants if hasattr(dspy_response, 'variants') else {}
                                limitations = dspy_response.limitations if hasattr(dspy_response, 'limitations') else []

                                # Log multi-hop RAG specific results
                                if intent:
                                    logger.info(f"Detected intent: {intent} (Category: {intent_category})")
                                if entities:
                                    logger.info(f"Detected entities: {', '.join(entities)}")
                                if step_by_step_guidance:
                                    logger.info(f"Found step-by-step guidance: {len(step_by_step_guidance)} steps")
                                if preconditions:
                                    logger.info(f"Found preconditions: {len(preconditions)}")

                                # Format response with all the enhanced information
                                base_response = dspy_response.answer
                                logger.info(f"DSPy answer retrieval results: {base_response}")

                                # If similar situations were used, highlight this
                                if similar_situations_used and similar_situations:
                                    # Inform the user that we're providing guidance from similar situations
                                    situation_intro = "\n\n**Note:** While I don't have specific information that directly answers your question, I've found similar situations in our documentation that might help:\n"
                                    similar_formatted = "\n".join([f"- {situation}" for situation in similar_situations[:2]])

                                    # Only add this if it's not already in the base response
                                    if "similar situation" not in base_response.lower():
                                        base_response += f"{situation_intro}{similar_formatted}"

                                # If query wasn't fully answered and we have suggested questions, add them to the response
                                if not query_answered and suggested_questions:
                                    questions_formatted = "\n".join([f"- {q}" for q in suggested_questions])
                                    base_response += f"\n\nTo better assist you, consider asking one of these more specific questions:\n{questions_formatted}"

                                # Format the final response - improved for procedural knowledge
                                ai_response = base_response

                                # Format procedural knowledge with better structure if available
                                procedural_content = []

                                # Add preconditions section if available and not already in response
                                if preconditions and len(preconditions) > 0 and "precondition" not in ai_response.lower() and "prerequisite" not in ai_response.lower():
                                    precond_formatted = "\n".join([f"- {precond}" for precond in preconditions])
                                    procedural_content.append(f"**Prerequisites/Preconditions:**\n{precond_formatted}")

                                # Add step-by-step guidance if available and not already in response
                                if step_by_step_guidance and len(step_by_step_guidance) > 0 and "step" not in ai_response.lower() and "procedure" not in ai_response.lower():
                                    steps_formatted = "\n".join([f"{i+1}. {step}" for i, step in enumerate(step_by_step_guidance)])
                                    procedural_content.append(f"**Step-by-Step Procedure:**\n{steps_formatted}")

                                # Add variants if available and not already in response
                                if variants and len(variants) > 0 and "variant" not in ai_response.lower() and "alternative" not in ai_response.lower():
                                    variants_content = []
                                    for context, variant_steps in variants.items():
                                        variant_formatted = "\n".join([f"- {step}" for step in variant_steps])
                                        variants_content.append(f"**For {context}:**\n{variant_formatted}")
                                    procedural_content.append("**Variations/Alternatives:**\n" + "\n\n".join(variants_content))

                                # Add limitations if available and not already in response
                                if limitations and len(limitations) > 0 and "limitation" not in ai_response.lower() and "important note" not in ai_response.lower():
                                    limits_formatted = "\n".join([f"- {limit}" for limit in limitations])
                                    procedural_content.append(f"**Important Notes/Limitations:**\n{limits_formatted}")

                                # Add technical details in a collapsible section if available and not already included
                                tech_details = []
                                if analysis and "analysis" not in ai_response.lower():
                                    tech_details.append(f"**Analysis:** {analysis}")

                                if extracted_steps and len(extracted_steps) > 0 and "extracted step" not in ai_response.lower() and "step" not in ai_response.lower():
                                    steps_formatted = "\n".join([f"{i+1}. {step}" for i, step in enumerate(extracted_steps)])
                                    tech_details.append(f"**Extracted Steps:** \n{steps_formatted}")

                                if rearticulated_queries and len(rearticulated_queries) > 0:
                                    # Don't show this in the UI, just for debugging
                                    logger.info(f"Rearticulated queries: {', '.join(rearticulated_queries[:3])}")

                                # Add intent and entities if available (for debugging)
                                if intent and "intent" not in ai_response.lower():
                                    logger.info(f"Intent: {intent} (Category: {intent_category or 'unknown'})")
                                if entities and len(entities) > 0:
                                    logger.info(f"Entities: {', '.join(entities)}")

                                # First add procedural content if available
                                if procedural_content and len(procedural_content) > 0:
                                    proc_content_html = "\n\n" + "\n\n".join(procedural_content)
                                    # Only add if the response is not already very long
                                    if len(ai_response) < 2000:  # Approximate limit to avoid very long responses
                                        ai_response += proc_content_html

                                # Then add technical details if they exist and aren't already in the response
                                if tech_details and len(tech_details) > 0:
                                    tech_details_html = "\n\n" + "\n\n".join(tech_details)
                                    # Only add if the response is not already very long
                                    if len(ai_response) < 3000 and len(ai_response) + len(tech_details_html) < 4000:  # Approximate limit to avoid very long responses
                                        ai_response += tech_details_html

                                dspy_response.sources if hasattr(dspy_response, 'sources') else []

                                # Calculate generation time
                                generate_time = time.time() - generate_start
                                logger.info(f"Generated response in {generate_time:.2f}s using DSPy chatbot with reasoning")

                            else:
                                # Fall back to SonnetLLM implementation
                                llm = legal_components["llm"]

                                # Use the unified RAG implementation with use_multihop parameter
                                try:
                                    # Default to using procedural RAG (multi-hop) for better responses
                                    use_multihop = True
                                    logger.info("Using procedural RAG response generation with SonnetLLM")
                                    response = llm.generate_rag_response(
                                        query=user_input,
                                        retrieved_results=retrieved_results,
                                        temperature=0.1,
                                        max_tokens=2048,
                                        use_multihop=use_multihop
                                    )
                                except Exception as e:
                                    logger.error(f"Error using RAG with use_multihop=True: {str(e)}")
                                    logger.info("Falling back to standard RAG due to error")
                                    # Fall back to standard RAG if procedural RAG fails
                                    try:
                                        response = llm.generate_rag_response(
                                            query=user_input,
                                            retrieved_results=retrieved_results,
                                            temperature=0.1,
                                            max_tokens=2048,
                                            use_multihop=False
                                        )
                                    except Exception as e2:
                                        logger.error(f"Error using standard RAG: {str(e2)}")
                                        logger.info("Falling back to basic generation")
                                        # Fall back to basic generation if all RAG methods fail
                                        response = llm.generate(
                                            prompt=user_input,
                                            temperature=0.1,
                                            max_tokens=2048
                                        )

                                ai_response = response["content"]
                                response.get("sources", [])
                                generate_time = time.time() - generate_start
                                logger.info(f"Generated response in {generate_time:.2f}s using direct SonnetLLM")

                            # Cache the new response if caching is enabled
                            if st.session_state.legal_enable_caching and "cache_vectorizer" in legal_components:
                                # Implement a simple LRU cache - remove oldest entry when cache gets too large
                                if len(st.session_state.legal_response_cache) >= 50:  # Limit cache size
                                    oldest_key = next(iter(st.session_state.legal_response_cache))
                                    del st.session_state.legal_response_cache[oldest_key]

                                # Store response in cache with embedding for future similarity search
                                st.session_state.legal_response_cache[user_input] = {
                                    "response": ai_response,
                                    "retrieved_results": retrieved_results,
                                    "embedding": query_embedding,
                                    "timestamp": datetime.now().isoformat()
                                }

                        # Add AI response to chat history and update UI state
                        st.session_state.legal_chat_history.append(
                            AIMessage(content=ai_response, additional_kwargs={"role": "assistant"})
                        )

                        # Add to chat message for saving
                        chat_message.append({'role': "assistant", 'content': ai_response})

                        # Update session state with results
                        message_id = f"legal_msg_{len(st.session_state.legal_chat_history)}"
                        message_obj = {
                            "message_id": message_id,
                            "query": user_input,
                            "response": {
                                "msg": ai_response,
                                "search_time": retrieve_time + generate_time,
                                "from_cache": from_cache
                            }
                        }
                        st.session_state.legal_message_obj = message_obj

                        if retrieved_results:
                            # Create an enhanced structured object for the retrieved documents
                            retrieved_docs = []
                            for i, result in enumerate(retrieved_results):
                                # Get basic document info
                                score = result.get('score', 0)
                                doc_id = result.get('document_id', f"Document {i+1}")
                                content = result.get('content', '')
                                metadata = result.get('metadata', {})

                                # Create relevance categories for better UI presentation
                                relevance_category = result.get('relevance_category',
                                                             'high' if score > 0.8 else
                                                             'medium' if score > 0.5 else 'low')

                                # Add document with enhanced metadata
                                retrieved_docs.append({
                                    'score': score,
                                    'document_id': doc_id,
                                    'content': content,
                                    'metadata': metadata,
                                    'relevance_category': relevance_category
                                })

                            # Sort documents by relevance score (highest first)
                            retrieved_docs.sort(key=lambda x: x['score'], reverse=True)

                            # Store in session state for rendering after rerun
                            st.session_state.legal_retrieved_docs = retrieved_docs
                        st.session_state.legal_processing_complete = True

                        # Save complete conversation
                        full_chat_message = []
                        for msg in st.session_state.legal_chat_history:
                            if hasattr(msg, 'additional_kwargs') and msg.additional_kwargs.get("role"):
                                role = msg.additional_kwargs.get("role")
                                content_raw = msg.content
                            elif isinstance(msg, dict) and "role" in msg and "content" in msg:
                                role = msg["role"]
                                content_raw = msg["content"]
                            else:
                                continue

                            full_chat_message.append({'role': role, 'content': content_raw})

                        # Check if this is a new conversation (first message in thread)
                        is_new_conversation = len(st.session_state.legal_chat_history) == 2  # user + ai message

                        # Log and save the full conversation
                        logger.info(f"Saving complete conversation with {len(full_chat_message)} messages")
                        save_result = save_conversation(full_chat_message, prid, st.session_state['legal_thread_id'], "legal")
                        if not save_result:
                            logger.error("Failed to save complete conversation")
                            # Retry with just the current exchange as fallback
                            save_conversation(chat_message, prid, st.session_state['legal_thread_id'], "legal")

                        # Only reload conversations if this is a new conversation
                        # For existing conversations, the metadata doesn't change
                        if is_new_conversation:
                            st.session_state.legal_reload_conversations = True
                            # Reset the loaded flag to force reload on next render
                            st.session_state.legal_conversations_loaded = False

                        # Force streamlit to rerun and update the UI
                        st.rerun()  # Using st.rerun() instead of deprecated st.experimental_rerun()
                    except Exception as e:
                        logger.error(f"Error processing legal chat: {str(e)}")
                        st.error(f"An error occurred: {str(e)}")

                        # Fallback response in case of error
                        ai_response = "I'm sorry, I encountered an error processing your request. Please try again."
                        st.session_state.legal_chat_history.append(
                            AIMessage(content=ai_response, additional_kwargs={"role": "assistant"})
                        )
                        chat_message.append({'role': "assistant", 'content': ai_response})

                    # Set a flag to indicate processing is complete
                    st.session_state.legal_processing_complete = True

                with st.container(key="text_area_container"):
                    # Add a progress indicator while processing
                    if "pending_user_input" in st.session_state and st.session_state.pending_user_input and not st.session_state.legal_processing_complete:
                        with st.status("Processing your question...", expanded=True) as status:
                            st.write("🔍 Finding relevant legal documents...")
                            # The actual processing happens elsewhere, this is just for UI feedback
                            st.write("⚙️ Generating response with legal context...")
                            status.update(label="Preparing your answer", state="running")

                    # Improved input UI with better accessibility
                    col1, col2 = st.columns([6, 1])
                    with col1:
                        st.text_area(" ",
                                    placeholder="Ask your legal question here in any language..",
                                    key="user_input",  # Add key parameter to store in session state
                                    label_visibility="collapsed",
                                    disabled=st.session_state.get("pending_user_input", "") != "" and not st.session_state.legal_processing_complete,
                                    help="Type your legal question in any language and press Submit or Enter to get an answer")
                    with col2:
                        st.markdown('<div class="st-send-btn-col">', unsafe_allow_html=True)
                        submit_disabled = st.session_state.get("pending_user_input", "") != "" and not st.session_state.legal_processing_complete
                        button_label = "Submit" if not submit_disabled else "Processing..."
                        if st.button(button_label,
                                    key="send_btn",
                                    on_click=send_message,
                                    disabled=submit_disabled,
                                    type="primary"):  # Make button more prominent
                            pass
                        st.markdown('</div>', unsafe_allow_html=True)
                    components.html("""
                    <script>
                    // Smart focus management that doesn't interfere with text selection
                    let textareaInitialized = false;
                    let isUserInteracting = false;

                    function initializeTextarea() {
                        if (textareaInitialized) return;

                        console.log("Initializing textarea...");
                        const textarea = parent.document.querySelector('textarea');
                        if (textarea && !textarea.disabled) {
                            // Only focus on initial load, not on every DOM change
                            if (!textareaInitialized) {
                                setTimeout(() => {
                                    if (!isUserInteracting) {
                                        textarea.focus();
                                    }
                                }, 100);
                                textareaInitialized = true;
                            }

                            // Handle Enter key for submission
                            textarea.addEventListener("keydown", function(event) {
                                if (event.key === "Enter" && !event.shiftKey) {
                                    event.preventDefault();  // Prevent Enter from adding newline

                                    // Find and click the submit button if not disabled
                                    const submitButton = parent.document.querySelector('button[data-testid="baseButton-primaryFormSubmit"]');
                                    if (submitButton && !submitButton.disabled) {
                                        submitButton.click();
                                    }
                                }
                            });
                        }
                    }

                    // Track user interactions to avoid interfering with text selection
                    function trackUserInteractions() {
                        ['mousedown', 'mouseup', 'selectstart', 'select'].forEach(eventType => {
                            parent.document.addEventListener(eventType, () => {
                                isUserInteracting = true;
                                setTimeout(() => {
                                    isUserInteracting = false;
                                }, 1000); // Reset after 1 second
                            });
                        });
                    }

                    // Add smooth transition effects to chat bubbles (only once)
                    function addChatBubbleAnimations() {
                        const allChatBubbles = parent.document.querySelectorAll('.chat-bubble');
                        allChatBubbles.forEach(bubble => {
                            if (!bubble.classList.contains('animated')) {
                                bubble.style.transition = 'all 0.3s ease-in-out';
                                bubble.classList.add('animated');
                            }
                        });
                    }

                    // Initialize everything once
                    document.addEventListener('DOMContentLoaded', function() {
                        trackUserInteractions();
                        initializeTextarea();
                        addChatBubbleAnimations();
                    });

                    // Fallback initialization
                    setTimeout(() => {
                        trackUserInteractions();
                        initializeTextarea();
                        addChatBubbleAnimations();
                    }, 500);
                    </script>
                    """, height=0)
