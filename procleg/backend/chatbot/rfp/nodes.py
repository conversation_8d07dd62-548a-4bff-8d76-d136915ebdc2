"""RFP-specific Node Functions for the Multi-Agent Conversational System.

This module contains node functions for processing RFP creation and customization queries.
"""
import os
from datetime import datetime
from decimal import Decimal
from typing import TYPE_CHECKING, Literal

from langchain_core.messages import AIMessage
from langchain_core.prompts import PromptTemplate
from langgraph.types import Command
from pydantic import BaseModel, field_validator

from procleg.backend.chatbot.models import llm
from procleg.backend.chatbot.rfp.agents import rfp_retriever_agent
from procleg.backend.chatbot.rfp.tools import rfp_creation_tool
from procleg.logger_config import get_logger
if TYPE_CHECKING:
    from procleg.backend.chatbot.multiagent_graph import PersistentState

# Configure logging
logger = get_logger(__name__)

def rfp_retriever_node(state: "PersistentState") -> Command[Literal["supervisor"]]:
    try:
        logger.info("Invoking RFP retriever node")
        result = rfp_retriever_agent.invoke(state)
        logger.debug("RFP creation result obtained",)

        return Command(
            update={
                "messages": [
                    AIMessage(content=result["messages"][-1].content, name="rfp_creator")
                ],
                "last_msg": "AI",
                "render_md": False
            },
            goto="supervisor"
        )

    except Exception as e:
        # Log error and provide a fallback response
        logger.exception("Error in rfp_creation_node")
        error_message = ("I apologize, but I encountered an issue while processing your RFP request."
                         " Could you please rephrase or try a different question?")
        return Command(
            update={
                "messages": [
                    AIMessage(content=error_message, name="rfp_creator")
                ],
                "last_msg": "AI",
                "error": f"RFP creation error: {e!s}",
                "render_md": False
            },
            goto="supervisor"
        )


def rfp_creation_node(state: "PersistentState") -> Command[Literal["supervisor"]]:
    """Handle RFP creation queries using specialized tools and knowledge.

    This function processes user queries related to RFP creation, template customization,
    and document retrieval. It uses the rfp_creator_agent to generate appropriate responses
    based on the user's needs.

    Args:
    ----
        state (State): The current conversation state containing messages and metadata

    Returns:
    -------
        Command: A command with the agent's response and routing back to the supervisor
    """
    try:
        logger.info("Invoking RFP creator agent")

        #Analysing answers from the user
        template = PromptTemplate(
            template="""Analyse the chat history to find out what are the answers that the user has provided for
                these questions:
                1. If the project is considered as high risk (yes/no)
                2. If the user is working with new and unknown suppliers for this service category (yes/no)
                3. Are service levels relevant (yes/no)
                4. Is Exit Strategy relevant (yes/no)

                CHAT HISTORY: {chat_history}
                """,
            input_variables=['chat_history']
        )
        prompt = template.invoke({"chat_history": state['messages']})

        class RFPAnswers(BaseModel):
            high_risk: bool | None
            unknown_suppliers: bool | None
            service_levels_relevant: bool | None
            exit_strategy_relevant: bool | None

            @field_validator('*', mode='before')
            def handle_unknown(cls, v):
                if not isinstance(v, bool):
                    return None
                return v

        answers_obj = llm.with_structured_output(RFPAnswers).invoke(prompt)
        logger.debug("Answers object received: %s", answers_obj)

        answers_dict = answers_obj.dict()

        input_data = {**state, **answers_dict}

        field_to_question = {
            "high_risk": "Is the project considered high risk? ",
            "unknown_suppliers": "Are you working with new and unknown suppliers ?",
            "service_levels_relevant": "Are service levels or KPIs relevant?",
            "exit_strategy_relevant": "Is exit strategy relevant?"
        }

        #Asking user for all the fields
        rfp_field_questions = """
        (1)Is the project considered high risk?
        (2)Are you working with new and unknown suppliers ?
        (3)Are service levels or KPIs section relevant for the project?
        (4)Is exit strategy block relevant for the project?"""

        all_fields_template = PromptTemplate(template="""
        You are a helpful and professional Assistant.
        The user is in the process of creating an RFP (Request for Proposal).

        Respond by saying that in order to guide the user through the steps to create an RFP,
        you will need some answers from them & if the user to help you with the following
        questions: "{rfp_field_questions}"

        Just ask all these questions at once in a proper manner.
        Do not add any explanations, summaries, or small talk.
        Do not include any polite introductory words like 'Certainly', 'Of course' or similar phrases.
        """,

        input_variables=['rfp_field_questions'])

        all_fields_prompt = all_fields_template.invoke({'rfp_field_questions': rfp_field_questions})

        if all(value is None for value in answers_dict.values()):
            logger.info("No answers provided yet - requesting all fields")
            response = llm.invoke(all_fields_prompt).content
            return Command(
                update={
                    "messages": [AIMessage(content=response, name="rfp_creator")],
                    "last_msg": "AI"
                },
                goto="supervisor"
            )

        #Asking the user the questions which he didn't answer
        missing_field_template = PromptTemplate(template="""
                You are a helpful and professional Assistant.
                The user is in the process of creating an RFP (Request for Proposal).

                Your task is to ask a **natural and specific question** that reflects this intent: "{question_to_ask}"
                Use the chat history for context to frame your question appropriately: {chat_history}

                Do not add any explanations, summaries, or small talk.
                Just ask the question. Keep the question to-the-point & short.
                """,

                input_variables=['question_to_ask', 'chat_history'])

        for field, question in field_to_question.items():
            if input_data.get(field) is None:
                logger.info("Field '%s' is missing. Asking user: %s", field, question)
                missing_field_prompt = missing_field_template.invoke({'question_to_ask': question, 'chat_history': state["messages"]})
                response = llm.invoke(missing_field_prompt).content
                return Command(
                    update={
                        "messages": [AIMessage(content=response, name="rfp_creator")],
                        "last_msg": "AI"
                    },
                    goto="supervisor"
                )

        logger.info("All fields are present — invoking rfp_creation_tool...")

        result = rfp_creation_tool(answers_obj.high_risk, answers_obj.unknown_suppliers,
                          answers_obj.service_levels_relevant, answers_obj.exit_strategy_relevant)

        # print("---result---", result)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file_name = f"Compiled_rfp_{timestamp}.json"

        base_dir = os.path.dirname(os.path.abspath(__file__))
        output_dir = os.path.join(base_dir, "tmp")
        os.makedirs(output_dir, exist_ok=True)
        os.path.join(output_dir, output_file_name)

        def convert_decimal(obj):
            if isinstance(obj, list):
                return [convert_decimal(i) for i in obj]
            elif isinstance(obj, dict):
                return {k: convert_decimal(v) for k, v in obj.items()}
            elif isinstance(obj, Decimal):
                return float(obj)
            return obj

        clean_result = convert_decimal(result)

        logger.debug("RFP creation result obtained")

        return Command(
            update={
                "messages": [
                    AIMessage(content="You RFP has been created successfully.", name="rfp_creator")
                ],
                "last_msg": "AI",
                "render_md": True,
                "rfp_data": clean_result
            },
            goto="supervisor"
        )

    except Exception as e:
        # Log error and provide a fallback response
        logger.exception("Error in rfp_creation_node")
        error_message = ("I apologize, but I encountered an issue while processing your RFP request."
                         " Could you please rephrase or try a different question?")
        return Command(
            update={
                "messages": [
                    AIMessage(content=error_message, name="rfp_creator")
                ],
                "last_msg": "AI",
                "error": f"RFP creation error: {e!s}"
            },
            goto="supervisor"
        )

def rfp_modification_node(state: "PersistentState") -> Command[[Literal["supervisor"]]]:
    try:
        logger.info("Starting RFP modification process")

        rfp_data = state.get('rfp_data')
        # Extract all 'key' values into a list
        key_list = [item["key"] for item in rfp_data]
        logger.debug("Available sections: %s", key_list)

        SectionLiteral = Literal[*key_list]

        # Pydantic model with section as one key from the key_list
        class RFPSectionModel(BaseModel):
            section: SectionLiteral

        get_section_template = PromptTemplate(
            template="You are a helpful assistant. The user has asked you to change a particular "
                     "section from an RFP template."
                     "Analyse the user query to identify which section is he"
                     "referring to, from the given list of sections."
                     "<user_query>{user_query}</user_query>"
                     "<sections_list>{sections_list}</sections_list>",
            input_variables=['user_query', 'key_list']
        )

        get_section_prompt = get_section_template.invoke({'user_query': state['messages'][-1],
                                                          'sections_list': key_list})

        get_section_llm = llm.with_structured_output(RFPSectionModel)

        section_to_change = get_section_llm.invoke(get_section_prompt).section
        logger.info("Section identified for modification: %s", section_to_change)

        section_content = next(item["content"] for item in rfp_data if item["key"] == section_to_change)
        logger.debug("Original content for section %s: %s", section_to_change, section_content)

        prompt_template = PromptTemplate(
            template="""You are a helpful AI Assistant.
                    Your task is to fill in this section in the provided RFP as the user has asked.

                    <user_instructions> {user_instructions} </user_instructions>
                    <section_to_change> {section_content} </section_to_change>

                    You need to rewrite this section according to the user instructions
                    and return it.

                    REMINDER: Modify according to how the user has asked.
                    DO NOT add additional or introductory text.
                    DO NOT add any such text as: [Rest of the RFP remains unchanged].
                    """,
            input_variables=['user_instructions', 'section_content']
        )

        final_prompt = prompt_template.invoke({'user_instructions': state["messages"][-1].content,
                                'section_content': section_content})

        modified_section = llm.invoke(final_prompt).content
        logger.debug("Modified content generated for section %s", section_to_change)

        for item in rfp_data:
            if item["key"] == section_to_change:
                item["content"] = modified_section
                logger.info("Updated content for section %s", section_to_change)
                break

            # Optionally, write the updated rfp_data back to the file if you want to save the changes
        # with open(rfp_path, 'w', encoding='utf-8') as file:
        #     json.dump(rfp_data, file, ensure_ascii=False, indent=4)
        # logger.info("Modified RFP saved successfully")

        return Command(
            update={
                "messages": [
                    AIMessage(content="The RFP has been modified successfully.", name="rfp_modification_node")
                ],
                "last_msg": "AI",
                "render_md": True,
                "rfp_data": rfp_data
            },
            goto="supervisor"
        )

    except Exception as e:
        logger.exception("Failed to modify the RFP: %s", str(e))
        return Command(
            update={
                "messages": [
                    AIMessage(content="An error occurred while modifying the RFP.",
                              name="rfp_modification_node")
                ],
                "last_msg": "AI"
            },
            goto="supervisor"
        )
