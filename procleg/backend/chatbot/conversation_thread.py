import os
import json
from datetime import datetime
import concurrent.futures
from functools import lru_cache
from typing import Any
from dataclasses import dataclass
import time
from enum import Enum

from langchain_core.prompts import PromptTemplate
from pydantic import BaseModel

from procleg.backend.services import s3_services
from procleg.backend.services.conversation_thread_services import ConversationThreadModel
from dotenv import load_dotenv
from procleg.logger_config import get_logger
from procleg.backend.chatbot.models import llm

logger = get_logger(__name__)
load_dotenv()

S3_BUCKET = os.getenv('S3_BUCKET_NAME', 'aig-azcdi-us-alxn-procleg-ds-dev')

# Configuration classes and enums
class LoadMode(Enum):
    """Enumeration for different loading modes."""
    FULL = "full"  # Load complete conversation data
    METADATA_ONLY = "metadata_only"  # Load only thread metadata
    SUMMARY = "summary"  # Load metadata + conversation summary

class SortBy(Enum):
    """Enumeration for sorting options."""
    CREATED_AT = "createdAt"
    UPDATED_AT = "updatedAt"
    THREAD_NAME = "thread_name"
    THREAD_ID = "thread_id"

class SortOrder(Enum):
    """Enumeration for sort order."""
    ASC = "asc"
    DESC = "desc"

@dataclass
class ConversationLoadOptions:
    """Configuration options for loading conversations."""
    mode: LoadMode = LoadMode.FULL
    page_size: int | None = None  # None means load all
    page_offset: int = 0
    sort_by: SortBy = SortBy.UPDATED_AT
    sort_order: SortOrder = SortOrder.DESC
    date_from: datetime | None = None
    date_to: datetime | None = None
    include_empty: bool = True  # Include threads with no messages
    max_workers: int = 10
    retry_attempts: int = 3
    retry_delay: float = 1.0  # seconds
    cache_ttl: int = 300  # seconds (5 minutes)

@dataclass
class LoadMetrics:
    """Performance metrics for conversation loading."""
    total_threads: int = 0
    successful_loads: int = 0
    failed_loads: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    total_time: float = 0.0
    s3_fetch_time: float = 0.0
    processing_time: float = 0.0

# Enhanced caching
_conversation_cache: dict[str, dict[str, Any]] = {}
_cache_timestamps: dict[str, float] = {}

def _get_cache_key(prid: str, module: str, options: ConversationLoadOptions) -> str:
    """Generate cache key for conversation data."""
    return f"{prid}:{module}:{options.mode.value}:{options.sort_by.value}:{options.sort_order.value}"

def _is_cache_valid(cache_key: str, ttl: int) -> bool:
    """Check if cached data is still valid."""
    if cache_key not in _cache_timestamps:
        return False
    return time.time() - _cache_timestamps[cache_key] < ttl

def _get_from_cache(cache_key: str, ttl: int) -> dict[str, Any] | None:
    """Get data from cache if valid."""
    if _is_cache_valid(cache_key, ttl):
        return _conversation_cache.get(cache_key)
    return None

def _put_in_cache(cache_key: str, data: dict[str, Any]) -> None:
    """Store data in cache with timestamp."""
    _conversation_cache[cache_key] = data
    _cache_timestamps[cache_key] = time.time()

def _retry_operation(operation, max_attempts: int = 3, delay: float = 1.0, *args, **kwargs):
    """Retry an operation with exponential backoff."""
    for attempt in range(max_attempts):
        try:
            return operation(*args, **kwargs)
        except Exception as e:
            if attempt == max_attempts - 1:
                raise e
            wait_time = delay * (2 ** attempt)
            logger.warning(f"Operation failed (attempt {attempt + 1}/{max_attempts}), retrying in {wait_time}s: {e}")
            time.sleep(wait_time)

@lru_cache(maxsize=100, typed=True)
def _fetch_and_parse_thread(text_s3_url: str, thread_id: str, retry_attempts: int = 3) -> dict | None:
    """Fetch and parse a single conversation thread from S3 with caching and retry logic.

    Args:
        text_s3_url (str): S3 URL of the conversation data
        thread_id (str): Thread ID for error logging
        retry_attempts (int): Number of retry attempts for transient failures

    Returns:
        Optional[dict]: Parsed conversation data or None if error occurs
    """
    try:
        s3_data = _retry_operation(
            s3_services.read_from_s3_file,
            retry_attempts,
            1.0,
            text_s3_url,
            S3_BUCKET
        )
        if not s3_data:
            logger.error("Failed to read S3 data for thread %s", thread_id)
            return None

        return json.loads(s3_data)
    except (json.JSONDecodeError, Exception) as e:
        logger.error("Error processing thread %s: %s", thread_id, e)
        return None

def _create_metadata_only_conversation(thread: ConversationThreadModel, prid: str) -> dict[str, Any]:
    """Create a metadata-only conversation object."""
    return {
        "thread_id": thread.thread_id,
        "prid": prid,
        "text": [],  # Empty for metadata-only
        "createdAt": thread.createdAt.isoformat() if hasattr(thread, 'createdAt') and thread.createdAt else datetime.now().isoformat(),
        "updatedAt": thread.updatedAt.isoformat() if hasattr(thread, 'updatedAt') and thread.updatedAt else datetime.now().isoformat(),
        "thread_name": getattr(thread, 'thread_name', 'unknown'),
        "lastModifiedBy": getattr(thread, 'lastModifiedBy', prid),
        "module": thread.module,
        "message_count": 0,  # Will be populated if available
        "last_message_at": None,
        "metadata_only": True
    }

def _create_full_conversation(thread: ConversationThreadModel, data: list[dict], prid: str) -> dict[str, Any]:
    """Create a full conversation object with all message data."""
    if not data:
        return _create_metadata_only_conversation(thread, prid)

    thread_metadata = data[0] if data else {}
    messages = data[1:] if len(data) > 1 else []

    return {
        "thread_id": thread_metadata.get("thread_id", thread.thread_id),
        "prid": prid,
        "text": [
            {
                "role": message.get("role", ""),
                "content": message.get("content", "")
            }
            for message in messages
        ],
        "createdAt": thread_metadata.get("conversation_started_at",
                                       thread.createdAt.isoformat() if hasattr(thread, 'createdAt') and thread.createdAt else datetime.now().isoformat()),
        "updatedAt": thread.updatedAt.isoformat() if hasattr(thread, 'updatedAt') and thread.updatedAt else datetime.now().isoformat(),
        "thread_name": thread_metadata.get("thread_name", getattr(thread, 'thread_name', 'unknown')),
        "lastModifiedBy": getattr(thread, 'lastModifiedBy', prid),
        "module": thread_metadata.get("module", thread.module),
        "message_count": len(messages),
        "last_message_at": thread.updatedAt.isoformat() if hasattr(thread, 'updatedAt') and thread.updatedAt else None,
        "metadata_only": False
    }

def _filter_threads_by_date(threads: list[ConversationThreadModel], options: ConversationLoadOptions) -> list[ConversationThreadModel]:
    """Filter threads by date range if specified."""
    if not options.date_from and not options.date_to:
        return threads

    filtered = []
    for thread in threads:
        thread_date = getattr(thread, 'updatedAt', None) or getattr(thread, 'createdAt', None)
        if not thread_date:
            continue

        if options.date_from and thread_date < options.date_from:
            continue
        if options.date_to and thread_date > options.date_to:
            continue

        filtered.append(thread)

    return filtered

def _sort_threads(threads: list[ConversationThreadModel], options: ConversationLoadOptions) -> list[ConversationThreadModel]:
    """Sort threads based on the specified criteria."""
    def get_sort_key(thread):
        if options.sort_by == SortBy.CREATED_AT:
            return getattr(thread, 'createdAt', datetime.min)
        elif options.sort_by == SortBy.UPDATED_AT:
            return getattr(thread, 'updatedAt', datetime.min)
        elif options.sort_by == SortBy.THREAD_NAME:
            return getattr(thread, 'thread_name', '').lower()
        elif options.sort_by == SortBy.THREAD_ID:
            return thread.thread_id
        return datetime.min

    reverse = options.sort_order == SortOrder.DESC
    return sorted(threads, key=get_sort_key, reverse=reverse)

def _apply_pagination(threads: list[ConversationThreadModel], options: ConversationLoadOptions) -> list[ConversationThreadModel]:
    """Apply pagination to the thread list."""
    if options.page_size is None:
        return threads

    start_idx = options.page_offset * options.page_size
    end_idx = start_idx + options.page_size
    return threads[start_idx:end_idx]

def load_conversations_optimized(
    prid: str,
    module: str = 'procurement',
    options: ConversationLoadOptions | None = None
) -> tuple[list[dict], LoadMetrics]:
    """
    Optimized conversation loading with configurable options and performance metrics.

    Args:
        prid (str): Project ID to load conversations for
        module (str): Module name, defaults to 'procurement'
        options (ConversationLoadOptions): Loading configuration options

    Returns:
        tuple: (conversations list, performance metrics)
    """
    if options is None:
        options = ConversationLoadOptions()

    metrics = LoadMetrics()
    start_time = time.time()

    logger.info("Loading conversations for PRID: %s, module: %s, mode: %s",
                prid, module, options.mode.value)

    # Check cache first
    cache_key = _get_cache_key(prid, module, options)
    cached_data = _get_from_cache(cache_key, options.cache_ttl)
    if cached_data:
        metrics.cache_hits = 1
        metrics.total_time = time.time() - start_time
        logger.info("Returning cached data for %s conversations", len(cached_data['conversations']))
        return cached_data['conversations'], metrics

    metrics.cache_misses = 1

    try:
        # Fetch threads from database
        db_start = time.time()
        threads = ConversationThreadModel.get_threads_by_prid_and_module(prid, module)
        time.time() - db_start

        if not threads:
            logger.warning("No conversation history found for PRID: %s", prid)
            empty_result = []
            _put_in_cache(cache_key, {'conversations': empty_result, 'metrics': metrics})
            metrics.total_time = time.time() - start_time
            return empty_result, metrics

        metrics.total_threads = len(threads)

        # Apply filtering
        threads = _filter_threads_by_date(threads, options)

        # Apply sorting
        threads = _sort_threads(threads, options)

        # Apply pagination
        threads = _apply_pagination(threads, options)

        conversations = []

        # Handle metadata-only mode
        if options.mode == LoadMode.METADATA_ONLY:
            processing_start = time.time()
            for thread in threads:
                conversation_data = _create_metadata_only_conversation(thread, prid)
                conversations.append(conversation_data)
                metrics.successful_loads += 1
            metrics.processing_time = time.time() - processing_start

        else:
            # Handle full loading with parallel processing
            s3_start = time.time()

            with concurrent.futures.ThreadPoolExecutor(max_workers=min(options.max_workers, len(threads))) as executor:
                # Create futures for each thread
                future_to_thread = {
                    executor.submit(_fetch_and_parse_thread, thread.text_s3_url, thread.thread_id, options.retry_attempts): thread
                    for thread in threads
                }

                processing_start = time.time()

                # Process completed futures as they finish
                for future in concurrent.futures.as_completed(future_to_thread):
                    thread = future_to_thread[future]
                    try:
                        data = future.result()
                        if data is None:
                            if options.include_empty:
                                # Include as metadata-only if S3 fetch failed but we want to include empty
                                conversation_data = _create_metadata_only_conversation(thread, prid)
                                conversations.append(conversation_data)
                            metrics.failed_loads += 1
                            continue

                        conversation_data = _create_full_conversation(thread, data, prid)
                        conversations.append(conversation_data)
                        metrics.successful_loads += 1

                        logger.debug("Loaded conversation thread: %s", conversation_data["thread_id"])
                    except Exception as e:
                        logger.error("Error processing thread %s: %s", thread.thread_id, e)
                        metrics.failed_loads += 1

                        if options.include_empty:
                            # Include as metadata-only on error
                            conversation_data = _create_metadata_only_conversation(thread, prid)
                            conversations.append(conversation_data)

            metrics.s3_fetch_time = time.time() - s3_start
            metrics.processing_time = time.time() - processing_start

        # Cache the results
        result_data = {'conversations': conversations, 'metrics': metrics}
        _put_in_cache(cache_key, result_data)

        metrics.total_time = time.time() - start_time

        logger.info("Successfully loaded %d conversations (success: %d, failed: %d, total_time: %.2fs)",
                   len(conversations), metrics.successful_loads, metrics.failed_loads, metrics.total_time)

        return conversations, metrics

    except Exception as e:
        logger.exception("Error loading conversations: %s", e)
        metrics.total_time = time.time() - start_time
        return [], metrics

def load_conversations(prid: str, module: str = 'procurement') -> list[dict]:
    """Load conversation threads for a given project ID using parallel processing.

    This function maintains backward compatibility while using the optimized implementation.

    Args:
        prid (str): Project ID to load conversations for
        module (str): Module name, defaults to 'procurement'

    Returns:
        list[dict]: List of conversation thread data dictionaries
    """
    # Use the optimized version with default options for backward compatibility
    conversations, metrics = load_conversations_optimized(prid, module)

    # Log performance metrics for monitoring
    logger.info("Load performance - Total: %.2fs, S3: %.2fs, Processing: %.2fs, Cache hits: %d",
               metrics.total_time, metrics.s3_fetch_time, metrics.processing_time, metrics.cache_hits)

    return conversations

# Convenience functions for common use cases
def load_conversations_metadata_only(prid: str, module: str = 'procurement', page_size: int | None = None) -> tuple[list[dict], LoadMetrics]:
    """Load only conversation metadata for fast listing."""
    options = ConversationLoadOptions(
        mode=LoadMode.METADATA_ONLY,
        page_size=page_size,
        cache_ttl=600  # Cache metadata longer (10 minutes)
    )
    return load_conversations_optimized(prid, module, options)

def load_conversations_paginated(
    prid: str,
    module: str = 'procurement',
    page_size: int = 20,
    page_offset: int = 0,
    sort_by: SortBy = SortBy.UPDATED_AT,
    sort_order: SortOrder = SortOrder.DESC
) -> tuple[list[dict], LoadMetrics]:
    """Load conversations with pagination support."""
    options = ConversationLoadOptions(
        page_size=page_size,
        page_offset=page_offset,
        sort_by=sort_by,
        sort_order=sort_order
    )
    return load_conversations_optimized(prid, module, options)

def load_conversations_by_date_range(
    prid: str,
    module: str = 'procurement',
    date_from: datetime | None = None,
    date_to: datetime | None = None,
    metadata_only: bool = False
) -> tuple[list[dict], LoadMetrics]:
    """Load conversations filtered by date range."""
    options = ConversationLoadOptions(
        mode=LoadMode.METADATA_ONLY if metadata_only else LoadMode.FULL,
        date_from=date_from,
        date_to=date_to,
        sort_by=SortBy.UPDATED_AT,
        sort_order=SortOrder.DESC
    )
    return load_conversations_optimized(prid, module, options)

def clear_conversation_cache(prid: str | None = None, module: str | None = None) -> int:
    """Clear conversation cache entries. Returns number of entries cleared."""
    if prid is None and module is None:
        # Clear all cache
        count = len(_conversation_cache)
        _conversation_cache.clear()
        _cache_timestamps.clear()
        return count

    # Clear specific entries
    keys_to_remove = []
    for key in _conversation_cache.keys():
        if prid and module:
            if key.startswith(f"{prid}:{module}:"):
                keys_to_remove.append(key)
        elif prid:
            if key.startswith(f"{prid}:"):
                keys_to_remove.append(key)
        elif module:
            parts = key.split(":")
            if len(parts) >= 2 and parts[1] == module:
                keys_to_remove.append(key)

    for key in keys_to_remove:
        _conversation_cache.pop(key, None)
        _cache_timestamps.pop(key, None)

    return len(keys_to_remove)

def save_conversation(chat_history: list[dict[str, str]], prid: str, thread_id: str, module: str) -> bool:
    """Persist conversation history to JSON files.

    This function saves the conversation history to JSON files organized by PRID
    (Project ID) and thread ID. It appends new messages to existing conversations
    or creates new conversation files as needed.

    Args:
    ----
        chat_history (List[Dict[str, str]]): The conversation history to save
        prid (str): The Project ID for organizing conversation files
        thread_id (str): The thread ID for the specific conversation
        module (str): Module name for which the conversation is saved

    Returns:
    -------
        bool: True if successful, False if an error occurred
    """
    try:
        # Input validation
        if not all([chat_history, prid, thread_id, module]):
            logger.error("Missing required parameters")
            return False

        if not isinstance(chat_history, list):
            logger.error("Chat history must be a list")
            return False

        filename = f"conversation_{prid}_{thread_id}.json"
        file_path = f"/tmp/{filename}"

        # Get the latest exchange (last two messages)
        last_msgs_cnt = min(2, len(chat_history))  # Ensure we don't exceed list bounds
        latest_exchange = chat_history[-last_msgs_cnt:]

        logger.debug("Processing conversation thread: prid=%s, thread_id=%s", prid, thread_id)
        thread = ConversationThreadModel.get_thread_by_primary_key(thread_id, module)
        thread_name = f"Conversation {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        if thread:
            thread_name=thread.thread_name
            logger.info("Found existing conversation thread: %s", thread_id)
            s3_data = s3_services.read_from_s3_file(thread.text_s3_url, S3_BUCKET)
            if not s3_data:
                logger.error("Failed to read existing conversation from S3")
                return False
            try:
                existing_data = json.loads(s3_data)
            except json.JSONDecodeError as e:
                logger.error("Failed to parse existing conversation data: %s", e)
                return False
        else:
            logger.info("Creating new conversation thread: %s", thread_id)
            if not chat_history:
                logger.error("Cannot create new thread without chat history")
                return False

            template = PromptTemplate(
                template="Generate ONE short, descriptive title (3-7 words) that reflects the topic or intent of this question: {user_query}",
                input_variables=['user_query']
            )
            class ConversationTitle(BaseModel):
                title: str

            try:
                prompt = template.invoke({"user_query": chat_history[0]["content"]})
                result = llm.with_structured_output(ConversationTitle).invoke(prompt)
                thread_name = result.title
            except Exception as e:
                logger.error("Failed to generate thread title: %s", e)


            existing_data = [{
                "conversation_started_at": datetime.now().isoformat(),
                "thread_id": thread_id,
                "thread_name": thread_name,
                "module": module
            }]

        # Update conversation data
        existing_data.extend(latest_exchange)

        # Save to temporary file
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, "w") as file:
                json.dump(existing_data, file, indent=4)
        except Exception as e:
            logger.error("Failed to write conversation to file: %s", e)
            return False
        s3_url = s3_services.upload_dict_to_s3(existing_data,
                                               S3_BUCKET,
                                               f"conversation_thread/{filename}")
        # Upload to S3
        # s3_url = s3_services.upload_file_to_s3(file_path, 'conversation_thread', filename)
        if not s3_url:
            logger.error("Failed to upload conversation to S3")
            return False

        # Create or update thread record
        try:
            # Check if thread already exists
            existing_thread = ConversationThreadModel.get_thread_by_primary_key(thread_id, module)
            if existing_thread:
                # Update existing thread
                existing_thread.update_thread(
                    text_s3_url=s3_url,
                    last_modified_by=prid,
                    thread_name=thread_name
                )
                logger.info("Updated existing thread record: %s", thread_id)
            else:
                # Create new thread
                ConversationThreadModel.create_thread(
                    thread_id=thread_id,
                    module=module,
                    prid=prid,
                    text_s3_url=s3_url,
                    last_modified_by=prid,
                    thread_name=thread_name
                )
                logger.info("Created new thread record: %s", thread_id)
        except Exception as e:
            logger.error("Failed to create/update thread record: %s", e)
            return False

        # Invalidate cache for this prid/module combination
        cache_cleared = clear_conversation_cache(prid, module)
        if cache_cleared > 0:
            logger.debug("Cleared %d cache entries for prid=%s, module=%s", cache_cleared, prid, module)

        logger.info("Successfully saved conversation: thread_id=%s, s3_url=%s", thread_id, s3_url)
        return True

    except Exception as e:
        logger.exception("Unexpected error saving conversation: %s", e)
        return False
    finally:
        # Cleanup temporary file
        if 'file_path' in locals() and os.path.exists(file_path):
            try:
                os.remove(file_path)
            except OSError as e:
                logger.warning("Failed to remove temporary file %s: %s", file_path, e)
