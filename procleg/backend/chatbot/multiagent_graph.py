"""Multi-Agent Conversational System for RFP Process Management.

This module implements a multi-agent conversational system using LangGraph and LangChain frameworks
to handle different types of queries related to Request for Proposal (RFP) processes. The system
uses a supervisor-worker architecture to route user queries to specialized agents.

The system consists of the following components:
1. Supervisor node: Routes messages to appropriate specialized agents
2. RFP Creation node: Handles RFP creation queries
3. Adherence Checking node: Handles adherence checking
4. Scoring node: Handles vendor scoring
5. Misc node: Handles general conversation

Each agent is specialized for a specific task and uses tools to perform its functions.
The system maintains conversation state and can save conversation history.

This file serves as the main entry point for the multi-agent system and contains all
the common components previously in the common directory.
"""


import os
from typing import Literal

from dotenv import load_dotenv
from langchain_core.messages import AIMessage
from procleg.backend.chatbot.dynamodb_checkpointer import DynamoDBCheckpointer
from langgraph.graph import END, START, MessagesState, StateGraph
from langgraph.prebuilt import create_react_agent
from langgraph.types import Command
from pydantic import BaseModel

from procleg.backend.chatbot.adherence.nodes import adherence_checking_node
from procleg.backend.chatbot.supplier_intelligence.nodes import supplier_node
from procleg.backend.chatbot.rfp.nodes import rfp_creation_node, rfp_retriever_node, rfp_modification_node
from procleg.backend.chatbot.vendor_scoring.nodes import scoring_node
from procleg.backend.chatbot.models import llm


from procleg.logger_config import get_logger

logger = get_logger(__name__)

# LLM Configuration
load_dotenv()

S3_BUCKET = os.getenv('S3_BUCKET_NAME', 'aig-azcdi-us-alxn-procleg-ds-dev')
# State Management
# Define available agent members and routing options
members = ["rfp_creator", "rfp_retriever", "rfp_modifier", "supplier_node",
           "adherence_checker", "vendor_scorer", "misc_node"]
options = ["rfp_creator", "rfp_retriever", "rfp_modifier", "supplier_node",
           "adherence_checker", "vendor_scorer", "misc_node", "FINISH"]


class Router(BaseModel):
    """Defines the next worker to route to in the conversation flow.

    This class is used by the supervisor to determine which specialized agent
    should handle the current user query, or if the conversation should end.

    Attributes
    ----------
        next (Literal): The next agent to route to, or "FINISH" to end the conversation

    """

    next: Literal[*options]


class PersistentState(MessagesState):
    """Manages the conversation state throughout the multi-agent system with persistence.

    This class extends MessagesState to track additional information about
    the conversation flow, including which agent to route to next and
    who sent the last message. It ensures all keys are preserved during state transitions.

    Attributes
    ----------
        next (str): The next agent to route to
        last_msg (str): Indicates whether the last message was from "Human" or "AI"
        render_md: A Boolean value indicating whether the md should be displayed
        rfp_data: Data related to RFP processing
    """

    next: str
    last_msg: str
    render_md: bool
    rfp_data: str | None

    def __getitem__(self, key):
        """Get an item from the state dictionary.

        This method allows accessing state attributes using dictionary-style notation.

        Args:
            key: The key to retrieve from the state

        Returns:
            The value associated with the key
        """
        if key == "messages":
            return self.messages
        return self.__dict__.get(key)

    def get(self, key, default=None):
        """Get an item from the state dictionary with a default value.

        Args:
            key: The key to retrieve from the state
            default: The default value to return if the key is not found

        Returns:
            The value associated with the key, or the default value if not found
        """
        if key == "messages":
            return self.messages
        return self.__dict__.get(key, default)


# Agent Configurations
# Miscellaneous Task Agent Configuration
misc_agent = create_react_agent(
    llm,
    tools=[],
    prompt="""You are a helpful assistant that handles general conversation with users.
    Keep your response as short as 1-2 lines.
    Provide concise, friendly responses to general queries.
    About RFP and RFP validation and RFP creation"""
)


# Node Functions
def supervisor_node(state: PersistentState) -> Command[Literal[*members, "__end__"]]:
    """Routes messages to appropriate specialized agents based on user query.

    This function acts as a supervisor that analyzes the conversation state and decides
    which specialized agent should handle the current user query. It uses an LLM to make
    routing decisions based on the content of the messages.

    Args:
    ----
        state (PersistentState): The current conversation state containing messages and metadata

    Returns:
    -------
        Command: A command indicating which agent to route to next and updates to the state

    """
    try:
        # Supervisor prompt for routing decisions
        system_prompt = f"""You are a supervisor deciding the next step in a conversation flow.
            AVAILABLE OPTIONS: {options}

            DECISION RULES:
            1. If the last message is by the AI Assistant, respond with EXACTLY "FINISH".
            2. If the last message is by the user, choose the appropriate worker:
               - If the conversation is about the creation of an RFP/ RFP template → "rfp_creator"
               - If the user has asked to modify or change an RFP template or section → "rfp_modifier"
               - If the user asks the steps or process of RFP creation -> "rfp_creator"
               - If the user asks about definitions of affiliate language, RFPs, or procurement steps,
                 contract types (MSA, SA, SOW),  → "rfp_retriever"
               - If the user requests guidance on when to use specific clauses like DPA, PV, or CDA → "rfp_retriever"
               - If the user is referencing the source book, cheat sheet, or payment terms table → "rfp_retriever"
               - If the user is asking questions related to Suppliers & their capabilities -> "supplier_node"
               - For checking compliance or adherence to internal guidelines → "adherence_checker"
               - For vendor evaluation or scorecard-based ranking → "vendor_scorer"
               - For greetings, small talk, or unrelated queries → "misc_node"
            """

        # Prepare messages for the LLM with the system prompt
        messages = [
            {"role": "system", "content": system_prompt},
        ] + state["messages"]

        # Get routing decision from LLM
        response = llm.with_structured_output(Router).invoke(messages)


        # Determine where to route based on the response and last message role
        last_msg_role = state.get("last_msg", "Human")
        goto = response.next
        render_md = state.get("render_md", False)
        rfp_data = state.get("rfp_data")

        logger.debug("Render MD flag: %s", render_md)

        if goto == "FINISH" or last_msg_role == "AI":
            logger.info("Conversation complete, ending session")
            goto = END

        # Get all existing state keys
        if isinstance(state, dict):
            state_dict = dict(state)
        else:
            state_dict = dict(state.__dict__)

        # Ensure messages are preserved (they're not in __dict__)
        if "messages" in state_dict:
            del state_dict["messages"]

        # Update only the specific keys we want to change
        state_dict.update({
            "next": goto,
            "last_msg": "Human",
            "render_md": render_md,
            "rfp_data": rfp_data
        })

        logger.info("Supervisor routing to: %s", str(goto))

        # Return command with routing decision and state update
        return Command(goto=goto, update=state_dict)

    except Exception as e:
        # Log error and route to misc_node as fallback
        logger.exception("Error in supervisor_node")

        # Get all existing state keys
        if isinstance(state, dict):
            state_dict = dict(state)
        else:
            state_dict = dict(state.__dict__)

        # Ensure messages are preserved (they're not in __dict__)
        if "messages" in state_dict:
            del state_dict["messages"]

        # Update only the specific keys we want to change
        state_dict.update({
            "next": "misc_node",
            "last_msg": "Human",
            "render_md": False,
            "rfp_data": state.get("rfp_data"),
            "error": f"Routing error: {e!s}"
        })

        return Command(
            goto="misc_node",
            update=state_dict
        )


def misc_node(state: PersistentState) -> Command[Literal["supervisor"]]:
    """Handle general conversation and queries not specific to RFP processes.

    This function processes general user queries, greetings, and other conversation
    that doesn't fall into the specialized categories of RFP creation, adherence checking,
    or vendor scoring.

    Args:
    ----
        state (PersistentState): The current conversation state containing messages and metadata

    Returns:
    -------
        Command: A command with the agent's response and routing back to the supervisor

    """
    try:
        # Invoke the miscellaneous agent with the current state
        logger.info("Invoking miscellaneous conversation agent")
        result = misc_agent.invoke(state)

        # Log the result for debugging
        logger.debug("Miscellaneous conversation result obtained")

        logger.debug("Miscellaneous conversation result: %s", result)

        # Get all existing state keys
        if isinstance(state, dict):
            state_dict = dict(state)
        else:
            state_dict = dict(state.__dict__)

        # Ensure messages are preserved (they're not in __dict__)
        if "messages" in state_dict:
            del state_dict["messages"]

        # Update only the specific keys we want to change
        state_dict.update({
            "messages": [
                AIMessage(content=result["messages"][-1].content, name="misc_node")
            ],
            "last_msg": "AI",
            "render_md": False
        })

        # Return command with the agent's response
        return Command(
            update=state_dict,
            goto="supervisor",
        )
    except Exception as e:
        # Log error and provide a fallback response
        logger.exception("Error in misc_node")
        error_message = ("I apologize, but I encountered an issue processing your request. "
                       "Could you please try again?")

        # Get all existing state keys
        if isinstance(state, dict):
            state_dict = dict(state)
        else:
            state_dict = dict(state.__dict__)

        # Ensure messages are preserved (they're not in __dict__)
        if "messages" in state_dict:
            del state_dict["messages"]

        # Update only the specific keys we want to change
        state_dict.update({
            "messages": [
                AIMessage(content=error_message, name="misc_node")
            ],
            "last_msg": "AI",
            "render_md": False,
            "rfp_data": state.get("rfp_data"),
            "error": f"Miscellaneous conversation error: {e!s}"
        })

        return Command(
            update=state_dict,
            goto="supervisor",
        )

# Graph Construction
logger.info("Building multi-agent conversation graph")
builder = StateGraph(PersistentState)
builder.add_edge(START, "supervisor")
builder.add_node("supervisor", supervisor_node)

# Add nodes to the graph
builder.add_node("rfp_creator", rfp_creation_node)
builder.add_node("rfp_retriever", rfp_retriever_node)
builder.add_node("rfp_modifier", rfp_modification_node)
builder.add_node("adherence_checker", adherence_checking_node)
builder.add_node("vendor_scorer", scoring_node)
builder.add_node("misc_node", misc_node)
builder.add_node("supplier_node", supplier_node)

# Set up checkpointing for conversation state with DynamoDB for concurrency control
checkpointer = DynamoDBCheckpointer(max_retries=5, retry_delay=0.2)
multi_agent_graph = builder.compile(checkpointer=checkpointer)

# Log the graph structure for debugging
logger.debug("Multi-agent graph structure:")
logger.debug(multi_agent_graph.get_graph().draw_mermaid())


def get_chat_history(config: str) -> list[dict[str, str]]:
    """Extract chat history from the graph state.

    This function retrieves the conversation history from the graph state and
    formats it as a list of message dictionaries with role and content.

    Args:
    ----
        config (str): The configuration ID for retrieving the state

    Returns:
    -------
        List[Dict[str, str]]: A list of message dictionaries with role and content

    """
    try:
        logger.info("Retrieving chat history for config: %s", config)
        state_list = list(multi_agent_graph.get_state(config))
        chat_history = []

        if not state_list:
            logger.warning("No state found for config: %s", config)
            return []

        for msg in state_list[0]["messages"]:
            content = msg.content
            role = msg.type

            message_dict = {
                "role": role,
                "content": content
            }
            chat_history.append(message_dict)

        logger.debug("Retrieved %d messages from chat history", len(chat_history))
        return chat_history
    except Exception:
        logger.exception("Error retrieving chat history")
        return []
